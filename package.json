{"name": "revolved-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "axios": "^1.8.4", "clsx": "^2.1.1", "firebase": "^11.6.1", "framer-motion": "^12.9.2", "gsap": "^3.13.0", "lucide-react": "^0.483.0", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.2.3", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}}