import React from "react";

interface VerificationErrorSectionProps {
  title?: string;
  message?: string;
}

const VerificationErrorSection: React.FC<VerificationErrorSectionProps> = ({
  title = "We're sorry, but Revolved is not the right fit for you at this time.",
  message = "Revolved is not suitable for pregnant women, those breastfeeding or planning to become pregnant. Some of treatments available through Revolved could complicate your pregnancy journey.\n\nPlease get in touch with your GP, who can offer more suitable options.",
}) => {
  return (
    <div className="mx-auto flex w-full max-w-2xl flex-col gap-4 rounded-2xl bg-[#fff3f3] px-4 py-4 outline outline-offset-[-1px] outline-[#fecdca] sm:px-8 sm:py-6">
      <h2 className="font-henju text-lg font-medium text-[#b42318] sm:text-xl">{title}</h2>
      <p className="text-md font-light whitespace-pre-line text-[#b42318]">{message}</p>
    </div>
  );
};

export default VerificationErrorSection;
