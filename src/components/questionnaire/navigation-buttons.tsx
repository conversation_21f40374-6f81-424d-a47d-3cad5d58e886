"use client";

import { useQuestionnaire } from "@/context/questionnaire-context";
import { But<PERSON> } from "@/components/ui/button";
import React, { useMemo } from "react";

const NavigationButtons: React.FC = () => {
  const { mainQuestions, currentQuestion, handleContinue, hasError } =
    useQuestionnaire();

  // Memoize button text to prevent unnecessary re-renders
  const buttonText = useMemo(() => {
    if (!mainQuestions || !mainQuestions.length) return "Next";
    return currentQuestion === mainQuestions.length - 1 ? "Confirm" : "Next";
  }, [currentQuestion, mainQuestions]);

  return (
    <div className="mt-6 flex items-center justify-between gap-4">
      {/* {currentQuestion > 0 && (
        <button
          onClick={handleBack}
          className="hover:text-primary cursor-pointer text-2md font-medium text-[#a39f9f] sm:text-2md"
        >
          Back
        </button>
      )} */}
      <Button
        onClick={handleContinue}
        variant="primary"
        withArrow
        className="w-full min-w-32 truncate py-2 pr-2 pl-5 text-2md"
        disabled={hasError()}
      >
        {buttonText}
      </Button>
    </div>
  );
};

export default React.memo(NavigationButtons);
