"use client";

import { useQuestionnaire } from "@/context/questionnaire-context";
import React from "react";
import QuestionInputs from "./question-inputs";

interface FollowUpQuestionProps {
  followUpQuestionId: string;
}

const FollowUpQuestion: React.FC<FollowUpQuestionProps> = ({ followUpQuestionId }) => {
  const { followUpQuestions } = useQuestionnaire();

  if (!followUpQuestionId || !followUpQuestions[followUpQuestionId]) {
    return null;
  }

  const followUpQuestion = followUpQuestions[followUpQuestionId];
  console.log("followUpQuestion", followUpQuestion);

  return (
    <div className="mt-4">
      <div className="mb-4">
        <h2 className="text-2md mb-2 font-normal">{followUpQuestion.question}</h2>
        {followUpQuestion.description && (
          <p className="text-md font-light text-neutral-600">{followUpQuestion.description}</p>
        )}
      </div>
      <div className="w-full">
        <QuestionInputs question={followUpQuestion} />
      </div>
    </div>
  );
};

export default React.memo(FollowUpQuestion);
