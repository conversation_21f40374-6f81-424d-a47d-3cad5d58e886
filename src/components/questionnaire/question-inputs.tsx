"use client";

import { IQuestionnaireSingleItem } from "@/types/questionnaire";
import React from "react";
import TextInput from "./inputs/text-input";
import SelectInput from "./inputs/select-input";
import MultiSelectInput from "./inputs/multi-select-input";
import UnitInput from "./inputs/unit-input";

interface QuestionInputsProps {
  question: IQuestionnaireSingleItem;
}

const QuestionInputs: React.FC<QuestionInputsProps> = ({ question }) => {
  if (!question) return null;

  switch (question.type) {
    case "text":
    case "date":
      return <TextInput question={question} />;
    case "select":
      return <SelectInput question={question} />;
    case "multiselect":
      return <MultiSelectInput question={question} />;
    case "number":
      return <UnitInput question={question} />;
    default:
      return <TextInput question={question} />;
  }
};

export default React.memo(QuestionInputs);
