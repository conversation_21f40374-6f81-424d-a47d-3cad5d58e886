import { useGetPublicCategories } from "@/api/category-service";
import Link from "next/link";
import { useMemo } from "react";
import RevolvedLogo from "../nav/nav-bar-logo";
import { Button } from "../ui/button";
import Image from "next/image";

interface SelectorContent {
  _id: string;
  title: string;
  icon: string;
}

interface WelcomeSectionProps {
  onStart: () => void;
  onChangeCategory: (categoryId: string) => void;
  currentCategoryId: string | null;
}

// Welcome component for the questionnaire start page
const WelcomeSection = ({ onStart, onChangeCategory, currentCategoryId }: WelcomeSectionProps) => {
  const { categories, categoriesLoading, categoriesError, categoriesEmpty } =
    useGetPublicCategories({ page: 0, limit: 100 });

  const treatmentContents: SelectorContent[] = useMemo(() => {
    if (categoriesLoading || categoriesError || categoriesEmpty || !categories.length) {
      return [];
    }

    return categories.map((category, index) => ({
      _id: category._id,
      title: category.name,
      icon: `/images/header/icons/icon${index + 1}.svg`,
    }));
  }, [categories, categoriesLoading, categoriesError, categoriesEmpty]);

  return (
    <div
      data-property-1="Default"
      className="inline-flex w-full flex-col items-center justify-start gap-7 pb-12 md:w-1/2 xl:w-1/3"
    >
      <Link href="/" className="h-16 w-36 overflow-hidden md:absolute md:top-4 md:left-10">
        <RevolvedLogo className="h-16 w-36" />
      </Link>
      <div className="w-full max-w-2xl justify-start">
        <p className="text-primary font-henju mb-2 text-3xl font-normal">Welcome to Revolved!</p>
        <p className="font-henju text-3xl font-normal text-black">What can we help you with?</p>
      </div>
      {/* <pre>{JSON.stringify(currentCategoryId, null, 2)}</pre> */}
      {/* <pre>{JSON.stringify(treatmentContents, null, 2)}</pre> */}
      <div className="grid w-full max-w-2xl grid-cols-2 gap-4">
        {treatmentContents.map((content, index) => (
          <div
            key={index}
            className={`flex cursor-pointer flex-row items-center justify-start gap-4 rounded-2xl p-4 outline-1 outline-offset-[-1px] transition-all duration-300 hover:shadow-sm ${
              currentCategoryId === content._id
                ? "bg-[#f3f8f6] shadow-sm outline-[#85b5a5]"
                : "bg-white outline-[#ededed]"
            }`}
            onClick={() => onChangeCategory(content._id)}
          >
            <Image
              src={content.icon}
              alt={content.title}
              width={32}
              height={32}
              className="h-8 w-8"
            />
            <p className="text-sm font-medium text-black">{content.title}</p>
          </div>
        ))}
      </div>
      <div className="text-md w-full max-w-2xl justify-start font-light text-black">
        Answer a few quick questions for your prescriber. Your response will be kept confidential
        between you and the medical team.
      </div>
      <div className="text-md w-full max-w-2xl justify-start font-light text-black">
        It won&apos;t take long—we promise! (It will only take 3-5 mins)
      </div>
      <Button
        onClick={onStart}
        variant="primary"
        withArrow
        className="text-2md w-full max-w-2xl min-w-32 truncate py-2 pr-2 pl-5"
      >
        Next
      </Button>
    </div>
  );
};

export default WelcomeSection;
