"use client";

import { useQuestionnaire } from "@/context/questionnaire-context";
import React from "react";
import VerificationErrorSection from "./verification-error-section";

const ValidationError: React.FC = () => {
  const { validationError, hasError, mainQuestions, currentQuestion, answers } = useQuestionnaire();

  // Find validation error based on rules
  const findValidationError = () => {
    if (!mainQuestions || !mainQuestions[currentQuestion]) return null;

    const currentQ = mainQuestions[currentQuestion];
    if (!currentQ || !currentQ.rules || !currentQ.rules.length) return null;

    const rule = currentQ.rules.find(
      (rule) =>
        rule.value && answers[currentQ._id]?.value?.toLowerCase() === rule.value.toLowerCase()
    );

    if (!rule || !rule.message) return null;

    // Handle message format - if it contains # it's split into title and message
    const messageParts = rule.message.includes("#")
      ? rule.message.split("#")
      : ["We're sorry, but Revolved is not the right fit for you at this time.", rule.message];

    return (
      <div className="mt-6">
        <VerificationErrorSection title={messageParts[0]} message={messageParts[1] || ""} />
      </div>
    );
  };

  // Show rule-based error
  if (hasError && hasError()) {
    return findValidationError();
  }

  // Show validation error
  if (validationError) {
    return (
      <div className="mt-6">
        <VerificationErrorSection title="Please complete this question" message={validationError} />
      </div>
    );
  }

  return null;
};

export default React.memo(ValidationError);
