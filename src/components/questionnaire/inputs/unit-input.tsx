"use client";

import { useQuestionnaire } from "@/context/questionnaire-context";
import { IQuestionnaireSingleItem } from "@/types/questionnaire";
import React, { useCallback } from "react";

interface UnitInputProps {
  question: IQuestionnaireSingleItem;
}

const UnitInput: React.FC<UnitInputProps> = ({ question }) => {
  const { answers, handleInputChange } = useQuestionnaire();

  if (!question || !question._id) return null;

  // Handle number input change
  const handleNumberChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const numericValue = e.target.value.replace(/\D/g, "");
      handleInputChange(question._id, "value", numericValue + ` ${question?.unit || "unit"}`);
    },
    [question._id, handleInputChange]
  );

  return (
    <div className="relative inset-0 mx-auto h-12 w-full rounded-2xl border border-[#e9e9e9] bg-white">
      <div className="absolute inset-0 flex items-center justify-center gap-2">
        <input
          type="text"
          placeholder="180"
          required={!question.isOptional}
          min={0}
          className="w-18 bg-transparent text-center text-3xl font-normal text-black focus:outline-none"
          value={answers[question._id]?.value?.replace(/\D/g, "") || ""}
          onChange={handleNumberChange}
        />
        <span className="text-primary text-2md font-normal">{question.unit || "unit "}</span>
      </div>
    </div>
  );
};

export default React.memo(UnitInput);
