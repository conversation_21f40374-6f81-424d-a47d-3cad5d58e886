"use client";

import { useQuestionnaire } from "@/context/questionnaire-context";
import { IQuestionnaireSingleItem } from "@/types/questionnaire";
import React, { useCallback } from "react";

interface TextInputProps {
  question: IQuestionnaireSingleItem;
}

const TextInput: React.FC<TextInputProps> = ({ question }) => {
  const { answers, handleInputChange } = useQuestionnaire();

  if (!question || !question._id) return null;

  // Handle text input change
  const handleTextChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      console.log("e.target.value", e.target.value);
      handleInputChange(question._id, "value", e.target.value);
    },
    [question._id, handleInputChange]
  );

  return (
    <div className="flex flex-col gap-6">
      <div className="w-full">
        <div className="relative h-12 w-full rounded-2xl border border-[#ededed] bg-white">
          <div className="text-2md text-center font-normal text-[#958e8e]">
            <input
              type={question.type === "date" ? "date" : "text"}
              placeholder="Your answer here."
              required={!question.isOptional}
              className="focus:border-primary h-11 w-[calc(100%_-_32px)] bg-white outline-none focus:outline-none"
              value={answers[question._id]?.value || answers[question._id]?.[question._id] || ""}
              onChange={handleTextChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(TextInput);
