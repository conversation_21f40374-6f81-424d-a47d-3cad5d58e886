"use client";

import { useQuestionnaire } from "@/context/questionnaire-context";
import { IQuestionnaireSingleItem } from "@/types/questionnaire";
import React, { useCallback } from "react";
import MCQCheckIcon from "@/components/icons/mcq-check-icon";
import FollowUpQuestion from "../follow-up-question";

interface MultiSelectInputProps {
  question: IQuestionnaireSingleItem;
}

const MultiSelectInput: React.FC<MultiSelectInputProps> = ({ question }) => {
  const { answers, handleCheckboxChange } = useQuestionnaire();

  if (!question || !question._id || !question.options) return null;

  // Handle checkbox toggle
  const handleOptionToggle = useCallback(
    (value: string) => {
      handleCheckboxChange(question._id, value);
    },
    [question._id, handleCheckboxChange]
  );

  return (
    <div className="flex flex-col gap-4">
      {question.options.map((input, index) => (
        <div key={index} className="mb-2">
          <div
            className={`flex h-12 w-full cursor-pointer items-center gap-4 rounded-2xl pr-4 pl-6 ${
              answers[question._id]?.[input.value]
                ? "border-primary border bg-[#85b5a5]/10"
                : "border border-[#e9e9e9] bg-white"
            }`}
            onClick={() => handleOptionToggle(input.value || "")}
          >
            <div className="relative size-6 overflow-hidden rounded-[8px] border-1 border-[#EDEDED] bg-white">
              {answers[question._id]?.[input.value] && (
                <div className="bg-primary relative size-7 overflow-hidden rounded-[8px]">
                  <div className="absolute top-1 left-1">
                    <MCQCheckIcon />
                  </div>
                </div>
              )}
            </div>
            <div className="text-2md leading-normal font-normal text-black">{input.label}</div>
          </div>

          {/* Render follow-up question if this option is selected and has a follow-up */}
          {answers[question._id]?.[input.value] && input.followUpQuestionId && (
            <FollowUpQuestion followUpQuestionId={input.followUpQuestionId} />
          )}
        </div>
      ))}
    </div>
  );
};

export default React.memo(MultiSelectInput);
