"use client";

import React from "react";
import RevolvedLogo from "@/components/nav/nav-bar-logo";
import QuestionnaireProgress from "./questionnaire-progress";
import QuestionDisplay from "./question-display";
import NavigationButtons from "./navigation-buttons";
import { useQuestionnaire } from "@/context/questionnaire-context";
import WelcomeSection from "./welcome-section";
import ValidationError from "./validation-error";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import PrebookingAnswersCompletedSection from "./prebooking-answers-completed-section";

const QuestionnaireLayout: React.FC = () => {
  const {
    showWelcome,
    handleStartQuestionnaire,
    currentQuestion,
    handleBack,
    handleChangeCategoryId,
    currentCategoryId,
    isFinishedUserPrebooking,
  } = useQuestionnaire();

  if (showWelcome) {
    return (
      <main className="flex min-h-screen flex-col items-center px-4 pt-12 pb-4 sm:px-6 sm:pt-20 md:pt-24">
        <WelcomeSection
          onStart={handleStartQuestionnaire}
          onChangeCategory={handleChangeCategoryId}
          currentCategoryId={currentCategoryId}
        />
      </main>
    );
  }

  // console.log("isFinishedUserPrebooking", isFinishedUserPrebooking);

  if (isFinishedUserPrebooking) {
    return (
      <main className="flex min-h-screen flex-col items-center px-4 pt-12 pb-4 sm:px-6 sm:pt-20 md:pt-24">
        <PrebookingAnswersCompletedSection />
      </main>
    );
  }

  return (
    <main className="flex min-h-screen flex-col items-center px-4 pt-12 pb-4 sm:px-6 sm:pt-20 md:px-8 md:pt-24 lg:px-12 lg:pt-52 xl:px-16">
      <Link href="/" className="overflow-hidden text-center md:absolute md:top-2 md:left-10">
        <RevolvedLogo className="h-20 w-36" />
      </Link>

      {currentQuestion > 0 && (
        <button
          onClick={handleBack}
          className="hover:text-primary text-2md sm:text-2md absolute top-8 right-10 cursor-pointer font-medium text-black"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
        </button>
      )}
      <div className="hidden h-[3px] w-full max-w-2xl flex-1 rounded-full md:absolute md:top-12 lg:block lg:w-1/2 xl:w-1/3">
        <QuestionnaireProgress />
      </div>

      <div className="w-full flex-1 lg:w-1/2 xl:w-1/3">
        <div className="mx-auto w-full max-w-2xl px-2">
          <div className="w-full lg:hidden">
            <QuestionnaireProgress />
          </div>
          <QuestionDisplay />
          <NavigationButtons />
          <ValidationError />
        </div>
      </div>
    </main>
  );
};

export default QuestionnaireLayout;
