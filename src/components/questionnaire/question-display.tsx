"use client";

import { useQuestionnaire } from "@/context/questionnaire-context";
import React from "react";
import QuestionInputs from "./question-inputs";

const QuestionDisplay: React.FC = () => {
  const { mainQuestions, currentQuestion } = useQuestionnaire();

  if (
    !mainQuestions ||
    !mainQuestions.length ||
    currentQuestion < 0 ||
    currentQuestion >= mainQuestions.length
  ) {
    return <div className="py-4 text-center">Loading questions...</div>;
  }

  const currentQ = mainQuestions[currentQuestion];

  return (
    <>
      <div className="mb-6">
        <h1 className="font-henju mb-3 text-2xl font-normal sm:mb-4 sm:text-3xl">
          {currentQ.question}
        </h1>
        {currentQ.description && (
          <p className="text-2md sm:text-2md text-neutral-600">{currentQ.description}</p>
        )}
      </div>

      <div className="w-full">
        <QuestionInputs question={currentQ} />
      </div>
    </>
  );
};

export default React.memo(QuestionDisplay);
