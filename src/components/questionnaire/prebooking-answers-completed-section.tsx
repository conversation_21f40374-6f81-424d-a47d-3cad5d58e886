import { useGetPrebookingAnswers } from "@/api/questionnaire-service";
import { useQuestionnaire } from "@/context/questionnaire-context";
import Link from "next/link";
import { useRouter } from "next/navigation";
import RevolvedLogo from "../nav/nav-bar-logo";
import { Button } from "../ui/button";

const PrebookingAnswersCompletedSection = () => {
  const { prebookingAnswers, prebookingAnswersLoading } = useGetPrebookingAnswers({
    limit: 100,
    page: 0,
  });
  const { updateAnswers, updateIsFinishedUserPrebooking, currentCategoryId, setIsEditMode } = useQuestionnaire();
  const router = useRouter();
  const handleEdit = () => {
    setIsEditMode(true);
    // Convert prebooking answers to the format expected by the questionnaire context
    const formattedAnswers = prebookingAnswers.reduce(
      (acc, answer) => {
        acc[answer.question._id] = {
          value: answer.answer,
          note: answer.note || "",
        };
        return acc;
      },
      {} as Record<string, any>
    );

    // Set the answers in the context
    updateAnswers(formattedAnswers);
    // Set isFinishedUserPrebooking to false to show the questionnaire
    updateIsFinishedUserPrebooking(false);
  };

  const handleConfirm = () => {
    // Set isFinishedUserPrebooking to true to hide the questionnaire
    updateIsFinishedUserPrebooking(true);
    router.push(`/booking?categoryId=${currentCategoryId}`);
  };

  // console.log("prebookingAnswers", prebookingAnswers);

  if (prebookingAnswersLoading) {
    return <div>Loading...</div>;
  }

  // Group answers: main and followups
  const mainAnswers = prebookingAnswers.filter((a) => !a.question.isFollowUp);
  const followupAnswers = prebookingAnswers.filter((a) => a.question.isFollowUp);

  console.log("mainAnswers", mainAnswers);
  console.log("followupAnswers", followupAnswers);

  return (
    <div className="inline-flex w-full flex-col items-center justify-start gap-7 pb-12 md:w-1/2 xl:w-1/3">
      <Link href="/" className="h-16 w-36 overflow-hidden md:absolute md:top-4 md:left-10">
        <RevolvedLogo className="h-16 w-36" />
      </Link>
      <div className="w-full max-w-2xl justify-start">
        <p className="font-henju pb-8 text-3xl font-normal text-black">
          Before we start, please confirm your details are up to date.
        </p>
        <p className="pb-6 text-base font-normal text-black">
          This is important for your practitioner to know if there have been any changes to your
          medical history. Your responses will be kept confidential between you and our medical
          team.
        </p>
        <div className="mb-6 inline-flex max-h-[400px] w-full flex-col items-start justify-start gap-2.5 overflow-y-auto rounded-[20px] bg-white px-[30px] py-5 outline outline-offset-[-1px] outline-[#e6e6e6]">
          {mainAnswers.map((main) => (
            <div key={main._id} className="w-full">
              <div className="inline-flex items-start justify-start gap-[150px] self-stretch">
                <div className="justify-start">
                  <span className="text-base font-normal text-[#cdcdcd]">
                    {main?.question.question}:
                  </span>
                  <span className="ml-2 text-base font-normal text-black capitalize">
                    {main.answer}
                  </span>
                </div>
              </div>
              {/* Show followups for this main question */}
              {followupAnswers
                .filter((f) => f.followUpTo?.question === main.question._id)
                .map((fu) => (
                  <div key={fu._id} className="mt-1 ml-8">
                    <span className="text-base font-normal text-[#cdcdcd]">
                      {fu?.question.question}:
                    </span>
                    <span className="ml-2 text-base font-normal text-black capitalize">
                      {fu.answer}
                    </span>
                  </div>
                ))}
            </div>
          ))}
        </div>
        <div className="flex w-full flex-row gap-4">
          <Button
            variant="outline"
            className="border-primary text-primary w-full"
            withArrow
            onClick={handleEdit}
          >
            Edit
          </Button>
          <Button withArrow variant="primary" className="w-full" onClick={handleConfirm}>
            Confirm
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PrebookingAnswersCompletedSection;
