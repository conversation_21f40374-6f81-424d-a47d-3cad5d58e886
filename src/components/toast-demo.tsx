"use client";

import React from "react";
import { useToast } from "@/context/toast-context";
import { Button } from "@/components/ui/button";

const ToastDemo: React.FC = () => {
  const { showToast } = useToast();

  const handleShowSuccessToast = () => {
    showToast("Operation completed successfully!", "success");
  };

  const handleShowErrorToast = () => {
    showToast("An error occurred. Please try again.", "error");
  };

  const handleShowInfoToast = () => {
    showToast("Here's some information for you.", "info");
  };

  const handleShowWarningToast = () => {
    showToast("Warning: This action cannot be undone.", "warning");
  };

  const handleShowMultipleToasts = () => {
    showToast("First notification with some longer text to demonstrate how it wraps", "info", 8000);

    setTimeout(() => {
      showToast("Second notification about a successful operation", "success", 8000);
    }, 300);

    setTimeout(() => {
      showToast(
        "Third notification with a warning message that you should be aware of",
        "warning",
        8000
      );
    }, 600);

    setTimeout(() => {
      showToast("Fourth notification with an error that needs your attention", "error", 8000);
    }, 900);

    setTimeout(() => {
      showToast("Fifth notification to show how many can be collapsed", "info", 8000);
    }, 1200);
  };

  return (
    <div className="flex flex-col gap-4 p-6">
      <h2 className="mb-4 text-2xl font-bold">Toast Notifications Demo</h2>
      <div className="flex flex-wrap gap-4">
        <Button onClick={handleShowSuccessToast} variant="primary">
          Show Success Toast
        </Button>
        <Button onClick={handleShowErrorToast} variant="primary">
          Show Error Toast
        </Button>
        <Button onClick={handleShowInfoToast} variant="primary">
          Show Info Toast
        </Button>
        <Button onClick={handleShowWarningToast} variant="primary">
          Show Warning Toast
        </Button>
        <Button onClick={handleShowMultipleToasts} variant="primary">
          Show Multiple Toasts
        </Button>
      </div>
      <div className="mt-4 space-y-2 text-2md text-gray-600">
        <p>
          When more than 3 toasts are shown, they will automatically collapse into a single
          notification. Click on the collapsed notification to expand and see all toasts.
        </p>
        <p>
          <strong>Hover Functionality:</strong> When you hover over a toast, it will remain visible
          until you move your mouse away. This prevents toasts from disappearing while you're
          reading them.
        </p>
        <p>
          <strong>Visual Feedback:</strong> Toasts will slightly elevate when hovered to indicate
          they won't auto-dismiss.
        </p>
        <p>
          <strong>Type-Specific Icons:</strong> Each toast type (success, error, warning, info)
          displays a different icon that visually communicates the nature of the notification.
        </p>
      </div>
    </div>
  );
};

export default ToastDemo;
