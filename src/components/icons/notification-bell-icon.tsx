import React from "react";
import Image from "next/image";

interface NotificationBellIconProps {
  className?: string;
}

const NotificationBellIcon = ({ className }: NotificationBellIconProps) => {
  const notificationCount = 0; // Replace with actual notification count logic
  return (
    <div className="relative">
      {notificationCount > 0 && (
        <p className="font-dm-sans absolute -top-2 -right-2 flex size-4 items-center justify-center rounded-full bg-[#23a1b0] text-[10px] font-medium text-white">
          {notificationCount}
        </p>
      )}
      <Image
        src="/icons/bell-icon.svg"
        alt="Bell Image"
        width={16}
        height={16}
        className="size-5 text-black"
      />
    </div>
  );
};

export default NotificationBellIcon;
