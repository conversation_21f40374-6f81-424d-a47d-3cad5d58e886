import React from "react";

interface ArrowRightIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  color?: string;
  strokeWidth?: number;
  height?: number;
  width?: number;
  className?: string;
}

const ArrowRightIcon: React.FC<ArrowRightIconProps> = ({
  width,
  height,
  size = 15,
  color = "#0C7885",
  strokeWidth = 1.5,
  className,
  ...props
}) => {
  return (
    <svg
      width={width ? width : size}
      height={height ? height : size}
      viewBox="0 0 15 16"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2.5 8.38379H12.5M12.5 8.38379L8.75 4.63379M12.5 8.38379L8.75 12.1338"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ArrowRightIcon;
