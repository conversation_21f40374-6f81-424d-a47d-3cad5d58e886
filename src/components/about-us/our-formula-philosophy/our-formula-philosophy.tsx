import React from "react";
import Image from "next/image";
import formulaImage from "@/assets/images/formula-dropper.png";

const FORMULA_POINTS = [
  "No paragons",
  "No sulfates",
  "No false promises",
  "Just clean, effective formulas your skin will thank you for.",
];

const OurFormulaPhilosophy = () => {
  return (
    <section className="w-full bg-white px-10 py-10 lg:px-6 lg:py-10">
      <div className="mx-auto text-center">
        <h2 className="font-henju text-2xl font-light text-black sm:text-3xl md:text-4xl lg:text-5xl">
          Our Formula <span className="text-[#4FA097]">Philosophy</span>
        </h2>
        <p className="font-quinn text-2md sm:text-2md mx-auto mt-2 w-full font-light text-black sm:w-1/2">
          Every ingredient we use has a purpose. We focus on actives that are backed by science,
          balanced with skin-soothing elements to keep your skin healthy, strong, and clear.
        </p>
      </div>

      <div className="mx-auto mt-10 flex flex-col items-center justify-center gap-10 sm:flex-col lg:flex-row">
        <div className="flex-shrink-0">
          <Image
            src={"/images/about/our-formula-img.png"}
            alt="Dropper Bottle"
            width={300}
            height={400}
            className="h-[200px] w-[300px] rounded-2xl object-contain sm:h-[600px] sm:w-[600px]"
          />
        </div>

        <div className="text-center lg:text-left">
          <h3 className="font-henju text-2xl font-light text-black sm:text-3xl md:text-4xl lg:text-5xl">
            Gentle. Proven
          </h3>
          <ul className="font-quinn text-2md mt-8 space-y-9 font-light text-black sm:text-2xl">
            {FORMULA_POINTS.map((point, index) => (
              <li key={index}>{point}</li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
};

export default OurFormulaPhilosophy;
