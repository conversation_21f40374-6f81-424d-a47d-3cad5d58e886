import CheckIconAboutUs from "@/components/icons/check-icon-about-us";
import React from "react";

const SOLUTIONS = [
  "Clinically proven results",
  "Suitable for all skin types",
  "No harsh chemicals",
  "Built for both men and women",
];

const WhyChooseRevolved = () => {
  return (
    <section className="mt-10 w-full bg-[#85B5A51A] px-4 py-10 md:mt-2 md:px-21 md:py-21">
      <div className="mx-auto max-w-3xl text-center">
        <h2 className="font-henju text-2xl font-light text-black sm:text-3xl md:text-4xl lg:text-5xl">
          Why Choose <span className="text-[#4FA097]">Revolved?</span>
        </h2>
        <p className="text-2md mx-auto mt-4 w-full font-light text-black md:w-1/2">
          We blend science with simplicity – using proven ingredients without overcomplicating your
          routine. Our products are clean, effective, and designed to work for real life – not just
          perfect skin.
        </p>

        <div className="mx-auto mt-8 flex w-full flex-col items-center gap-4">
          {SOLUTIONS.map((solution, index) => (
            <div key={index} className="text-2md flex items-center gap-3 font-light text-black">
              <CheckIconAboutUs />
              {solution}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseRevolved;
