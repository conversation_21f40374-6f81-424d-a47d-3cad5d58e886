import Image from "next/image";
import { ArrowRight } from "lucide-react";
import CheckIconAboutUs from "@/components/icons/check-icon-about-us";

export default function ExpertCareSection() {
  const BENEFITS = [
    "Supervised by licensed doctors – so you’re never on your own",
    "Tailored treatment plans – built around your needs and goals",
    "Ongoing support – real experts, real answers, anytime you need",
  ];

  return (
    <section className="mt-8 mr-0 ml-0 rounded-[50px] bg-[#F1FBFF] lg:m-14 lg:mt-14">
      <div className="mx-auto flex flex-col items-center justify-between gap-10 lg:flex-row">
        <div className="w-3.6/5 py-12 pl-12 text-left lg:py-21 lg:pl-21">
          <h2 className="font-henju text-center text-2xl font-light text-black sm:text-3xl md:text-4xl lg:text-start lg:text-5xl">
            Expert Care, <span className="text-[#0C7885]">Backed by Doctors</span>
          </h2>
          <p className="font-quinn text-2md sm:text-2md mt-2 w-full text-center font-light text-black lg:text-start">
            Our licensed medical team ensures every treatment is safe, effective, and built just for
            you.
          </p>

          <h3 className="font-henju mt-8 text-center text-2xl font-light text-black sm:text-4xl lg:text-start">
            Ready to Take the First Step?
          </h3>
          <p className="font-henju text-2md mt-2 text-center font-light text-black lg:text-start">
            Get the care you deserve—personalized, professional, and just a click away.
          </p>

          <ul className="text-2md m-auto mt-6 space-y-3 font-light text-black lg:ml-0">
            {BENEFITS.map((text, index) => (
              <li
                key={index}
                className="flex items-center justify-center gap-2 lg:items-start lg:justify-start"
              >
                <CheckIconAboutUs />
                <span>{text}</span>
              </li>
            ))}
          </ul>

          <button className="m-auto mt-8 flex w-2/3 items-center justify-between rounded-full bg-[#0C7885] px-6 py-3 text-white transition-colors hover:bg-[#0f6d77] lg:mt-8 lg:ml-0 lg:w-3/6">
            <span className="flex-grow text-center">Book a Consultation</span>
            <span className="ml-4 flex items-center justify-center rounded-full border border-white bg-white p-1">
              <ArrowRight size={14} className="text-[#0C7885]" />
            </span>
          </button>
        </div>

        <div className="h-[600px] flex-shrink-0">
          <Image
            src="/images/about/doctor.png"
            alt="Doctor"
            width={600}
            height={500}
            className="h-[100%] w-full object-contain"
          />
        </div>
      </div>
    </section>
  );
}
