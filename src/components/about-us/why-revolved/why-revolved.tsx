import CheckIconAboutUs from "@/components/icons/check-icon-about-us";
import { Button } from "@/components/ui/button";
import Image from "next/image";

const WhyRevolved = () => {
  const SOLUTIONS = [
    "Results-Driven: We use active ingredients that are backed by science.",
    "Clean & Conscious: Skin-safe, eco-conscious, and cruelty-free.",
    "Inclusive by Design: Our products are made for everyone — no matter your gender, tone, or type.",
  ];

  const CONTENT = [
    {
      title: "Our Vision",
      description:
        "To redefine wellness with accessible, science-backed solutions that empower people to live fully—every day.",
      bgClass: "bg-[#fff7f4]",
      textColor: "text-[#ffa068]",
      descriptionColor: "text-black",
    },
    {
      title: "Our Mision",
      description:
        "To create clean, effective tools that support recovery, sleep, and cognitive health—designed for real life and backed by licensed experts.",
      bgClass: "bg-[#fff7f4]",
      textColor: "text-[#ffa068]",
      descriptionColor: "text-black",
    },
    {
      title: "Our Promise",
      description:
        "Real results. No noise. We keep it clean, honest, and all about you—backed by licensed professionals and real feedback.",
      bgClass: "bg-gradient-to-l from-[#ffa068] to-[#ffb88d]",
      textColor: "text-white",
      descriptionColor: "text-white",
    },
  ];

  return (
    <div className="mt-26 flex w-full flex-col items-center gap-4 bg-white px-4 sm:px-6 md:mt-28 lg:gap-6 lg:px-10">
      <div className="w-full max-w-[1728px]">
        <h1 className="font-henju text-center text-2xl font-light text-black sm:text-3xl md:text-4xl lg:text-5xl">
          About <span className="text-[#4FA097]">Revolved</span>
        </h1>
        <div className="mb-8 flex justify-center md:mb-15">
          <p className="font-quinn text-2md lg:text-2md mt-1 flex w-full text-center font-thin md:w-1/2">
            Smart skincare for every face. At Revolved, we keep skincare simple and effective. Our
            clinically-backed, gender-neutral formulas deliver real results — for any skin type, any
            routine.
          </p>
        </div>
        <div className="flex flex-col gap-6 lg:flex-row lg:gap-6">
          <div
            id="content-about-revolved"
            className="m-auto h-[400px] w-full rounded-2xl bg-[#f4feff] px-5 pt-[40px] pb-4 sm:px-12 lg:ml-0 lg:h-[600px] lg:w-1/3 lg:pt-[140px]"
          >
            <h1 className="font-henju justify-start text-center text-4xl font-light text-black lg:text-left">
              Why Revolved?
            </h1>

            <div className="flex w-full max-w-[402px] flex-col gap-4 py-6">
              {SOLUTIONS.map((solution, index) => (
                <div key={solution} className="flex items-center gap-2">
                  <div data-svg-wrapper className="relative">
                    <CheckIconAboutUs />
                  </div>
                  <p className="text-2md font-light text-black">{solution}</p>
                </div>
              ))}
            </div>
            <Button
              variant="primary"
              withArrow
              className="text-2md m-auto mt-1 w-1/2 min-w-32 truncate bg-[#4FA097] py-2 pr-2 pl-4 md:mt-2 lg:ml-0"
            >
              Get Started
            </Button>
          </div>
          <div className="flex h-[400px] w-full gap-4 lg:h-[600px] lg:w-2/3">
            <div className="flex h-full w-1/2 flex-col gap-6">
              <Image
                src={"/images/about/about-img5.png"}
                alt={"about image 1"}
                width={600}
                height={240}
                className="h-3/4 w-full rounded-2xl object-cover object-top-left"
              />
              <div className="inline-flex h-1/4 w-full items-center justify-center gap-4 rounded-2xl bg-[#f4feff] md:gap-6">
                <div className="justify-center text-center">
                  <p className="font-henju text-2xl font-extralight text-[#0C7885]">10+</p>
                  <p className="text-2md text-2xl font-extralight text-[#0C7885]">
                    Years of experience
                  </p>
                </div>
              </div>
            </div>
            <div className="h-full w-1/2">
              <Image
                src={"/images/about/about-img4.png"}
                alt={"about image 1"}
                width={600}
                height={240}
                className="h-full w-full rounded-2xl object-cover object-top"
              />
            </div>
          </div>
        </div>
      </div>

      {/* <div className="flex w-full max-w-[1000px] flex-col gap-4 md:flex-row md:gap-6">
        <div className="relative h-[420px] w-full rounded-2xl bg-[#fff7f4] px-6 pt-8 pb-4 md:w-1/2">
          <div className="flex h-2/3 w-full gap-4">
            <h1 className="font-henju w-96 justify-start text-5xl leading-[54px] font-semibold text-[#ffa068] opacity-30">
              Our plan helps you feel more in control of your wellness
            </h1>
          </div>
          <Image
            src={"/images/about/about-img3.png"}
            alt={"about image 3"}
            width={540}
            height={540}
            className="absolute right-1/2 bottom-0 left-1/2 z-[1] h-full w-[420px] -translate-x-1/2 transform rounded-2xl"
          />
          <div className="absolute bottom-24 left-4 z-[2]">
            <Button
              variant="primary"
              withArrow
              className="min-w-32 truncate py-2 pr-2 pl-5 text-2md"
            >
              Contact Us
            </Button>
          </div>
        </div>
        <div className="grid h-[420px] w-full grid-rows-3 gap-4 md:w-1/2 md:gap-6">
          {CONTENT.map((item, index) => (
            <div
              key={index}
              className={`h-32 self-stretch px-4 pt-4 pb-[5px] ${item.bgClass} inline-flex flex-col items-start justify-start gap-4 rounded-2xl`}
            >
              <div
                className={`justify-start self-stretch ${item.textColor} font-henju text-xl font-normal`}
              >
                {item.title}
              </div>
              <div
                className={`justify-start self-stretch ${item.descriptionColor} text-md font-light`}
              >
                {item.description}
              </div>
            </div>
          ))}
        </div>
      </div> */}
    </div>
  );
};

export default WhyRevolved;
