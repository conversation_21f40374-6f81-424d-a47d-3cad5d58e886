import Badge from "@/components/ui/badge";
import Image from "next/image";

const trustPoints = ["Dermatologist-reviewed", "Clinically proven ingredients", "Made for all"];

const peopleImages = [
  "/images/about/trust-section-img4.png",
  "/images/about/trust-section-img2.png",
  "/images/about/trust-section-img1.png",
  "/images/about/trust-section-img3.png",
];

export default function TrustSection() {
  return (
    <section className="flex flex-col items-center bg-[#85b5a51a] px-6 py-18 text-center">
      <h2 className="font-henju text-2xl font-light text-black sm:text-3xl md:text-4xl lg:text-5xl">
        Trust in Science. <span className="text-[#4FA097]">Believe in Results</span>
      </h2>
      <p className="font-quinn text-2md sm:text-2md mx-auto mt-6 font-extralight text-black">
        Real results, no guesswork. Every product is tested for performance, safety, and
        sensitivity.
      </p>

      <div className="mt-3 flex flex-wrap justify-center gap-4">
        {trustPoints.map((point, index) => (
          <Badge
            key={index}
            variant="pill"
            size="lg"
            className="text-2md border border-[#4E9083] bg-transparent px-8 font-thin text-black"
          >
            {point}
          </Badge>
        ))}
      </div>

      <div className="mt-10 grid max-w-[1440px] grid-cols-1 place-items-center gap-6 sm:grid-cols-2 lg:grid-cols-4 lg:gap-12">
        {peopleImages.map((src, index) => (
          <div key={index} className="overflow-hidden rounded-2xl border border-white/60 p-2">
            <Image
              src={src}
              alt={`Person ${index + 1}`}
              width={300}
              height={300}
              className="h-[300px] w-full rounded-2xl object-cover sm:w-[300px]"
            />
          </div>
        ))}
      </div>
    </section>
  );
}
