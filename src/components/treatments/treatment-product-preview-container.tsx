import Image from "next/image";
import { useState } from "react";

interface ProductImage {
  id: number;
  src: string;
  alt: string;
}

const TreatmentProductPreviewContainer = () => {
  const productImages: ProductImage[] = [
    {
      id: 1,
      src: "/images/treatments/product1.png",
      alt: "Product 1",
    },
    {
      id: 2,
      src: "/images/treatments/product2.png",
      alt: "Product 1",
    },
    {
      id: 3,
      src: "/images/treatments/product3.png",
      alt: "Product 1",
    },
    {
      id: 4,
      src: "/images/treatments/product4.png",
      alt: "Product 1",
    },
  ];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handlePrevImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? productImages.length - 1 : prev - 1));
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prev) => (prev === productImages.length - 1 ? 0 : prev + 1));
  };

  const handleThumbnailClick = (index: number) => {
    setCurrentImageIndex(index);
  };
  return (
    <div className="flex aspect-square w-full max-w-5xl flex-col rounded-3xl">
      {/* Main Product Image */}
      <div className="relative h-full w-full bg-neutral-50 px-4 pt-8 pb-4 md:rounded-[45px]">
        <div className="relative h-full w-full p-4 md:p-8">
          <Image
            src={productImages[currentImageIndex].src}
            alt={productImages[currentImageIndex].alt}
            fill
            className="object-contain p-2"
            priority
          />
        </div>
      </div>

      <div className="flex w-full items-end justify-center gap-4 px-4 pt-12 pb-4">
        {productImages.map((image, index) => (
          <button
            key={image.id}
            onClick={() => handleThumbnailClick(index)}
            className={`relative size-12 overflow-hidden rounded-xl transition-transform hover:scale-105 md:size-16 ${
              currentImageIndex === index ? "ring-1 ring-[#ffa068]" : ""
            }`}
          >
            <Image src={image.src} alt={image.alt} fill className="object-cover" />
          </button>
        ))}
      </div>
    </div>
  );
};

export default TreatmentProductPreviewContainer;
