"use client";

import { ProductCard } from "@/components/product-card";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useRef } from "react";

interface SuggestedProductsProps {
  products: any;
}

export default function SuggestedProducts({ products }: SuggestedProductsProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = container.clientWidth * 0.8; // Scroll 80% of container width
      const scrollPosition =
        direction === "left"
          ? container.scrollLeft - scrollAmount
          : container.scrollLeft + scrollAmount;

      container.scrollTo({
        left: scrollPosition,
        behavior: "smooth",
      });
    }
  };

  if (!products || products.length === 0) {
    return null;
  }

  return (
    <div className="relative mx-auto w-full max-w-[1728px] px-2 py-4 md:px-12">
      <div className="relative px-4">
        {/* Scroll Container */}
        <div
          ref={scrollContainerRef}
          className="scrollbar-hide flex snap-x snap-mandatory gap-16 overflow-x-auto px-4"
        >
          {products.map((product: any, index: any) => (
            <div key={index} className="flex-none snap-start">
              <ProductCard product={product} />
            </div>
          ))}
        </div>

        {/* Navigation Buttons */}
        <button
          onClick={() => scroll("left")}
          className="group absolute top-1/2 -left-10 hidden size-10 -translate-y-1/2 items-center justify-center rounded-full bg-[#FAFAFA] transition-colors hover:bg-neutral-50 hover:shadow-sm md:flex"
          aria-label="Previous products"
        >
          <ArrowLeft className="size-5 stroke-1 transition-all group-hover:stroke-2" />
        </button>

        <button
          onClick={() => scroll("right")}
          className="group absolute top-1/2 -right-10 hidden size-10 -translate-y-1/2 items-center justify-center rounded-full bg-[#FAFAFA] transition-colors hover:bg-neutral-50 hover:shadow-sm md:flex"
          aria-label="Next products"
        >
          <ArrowRight className="size-5 stroke-1 transition-all group-hover:stroke-2" />
        </button>
      </div>
    </div>
  );
}
