"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import TreatmentProductPreviewContainer from "./treatment-product-preview-container";
import { CheckIcon } from "../icons/check-icon";

interface TreatmentProductDetailProps {}

const treatmentDetails = [
  "Exfoliates 30% faster than natural turnover",
  "Boosts collagen & hyaluronic acid",
  "Improves glow, firmness, and clarity",
  "Reduces fine lines, wrinkles, and dark spots",
  "Visible results from first night",
  "Soothes skin with silk proteins",
];

const productHighlights = [
  {
    title: "Natural",
    image: "/images/treatments/icon1.png",
  },
  {
    title: "Light weight",
    image: "/images/treatments/icon2.png",
  },
  {
    title: "Clean Ingredients",
    image: "/images/treatments/icon3.png",
  },
  {
    title: "Skincare for Everyone",
    image: "/images/treatments/icon4.png",
  },
];

export const TreatmentProductDetail = ({}: TreatmentProductDetailProps) => {
  return (
    <div className="flex w-full max-w-[1728px] flex-col gap-4 px-4 md:gap-8 md:px-8 lg:flex-row lg:gap-12 lg:pr-4 lg:pl-16">
      <div className="flex w-full flex-col gap-4 lg:w-[55%]">
        <TreatmentProductPreviewContainer />
      </div>
      <div className="flex w-full max-w-2xl flex-1 flex-col items-start justify-start gap-4 py-2 pr-4 pl-2 md:pr-16 lg:w-[45%]">
        <div className="flex flex-col gap-1">
          <h2 className="text-2xl font-light text-[#A7696A]">Skin Care</h2>
          <h1 className="text-3xl font-medium text-black">Bio-Oil Skincare</h1>
          <p className="text-md justify-start leading-normal font-light text-[#535353]">
            Bio-Oil Skincare Oil is a popular cosmetic oil designed to improve the appearance of the
            skin. Target fine lines, wrinkles, and sagging skin with clinically-proven treatments
            that restore firmness and glow. Powered by ingredients that boost collagen, hydrate
            deeply, and enhance skin’s elasticity for a youthful, radiant look. It is especially
            known for:
          </p>
        </div>

        <div className="flex w-full flex-col gap-4 rounded-[30px] bg-[#f3f8f6] py-6 pr-8 pl-6">
          {treatmentDetails.map((content, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className={`flex size-4 items-center justify-center rounded-full bg-[#4fa097]`}>
                <CheckIcon className="size-2.5" color="#fff" />
              </div>
              <p key={index} className="text-md md:text-md font-light text-black">
                {content}
              </p>
            </div>
          ))}
        </div>

        <h2 className="text-xl font-light text-black">Starting from $75.00 / month</h2>

        <Button
          withArrow
          variant="primary"
          className="w-2/3 max-w-2xl py-3 font-light"
          iconColor="#4fa097"
        >
          Get Started
        </Button>

        <div className="flex flex-col gap-4">
          <h2 className="text-2xl font-light text-black">Product Highlights</h2>
          <div className="flex flex-wrap gap-2">
            {productHighlights.map((highlight, index) => (
              <div
                key={index}
                className="inline-flex h-[124px] w-[120px] flex-col items-center justify-start gap-3 rounded-[20px] bg-[#f3f8f6] p-2.5"
              >
                <Image
                  src={highlight.image}
                  alt={`skin care icon`}
                  width={40}
                  height={40}
                  className="h-[32px] w-[32px] md:h-[40px] md:w-[40px]"
                />
                <h3 className="text-md text-center font-light text-black">{highlight.title}</h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
