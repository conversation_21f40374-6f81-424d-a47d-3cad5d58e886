"use client";
import { But<PERSON> } from "@/components/ui/button";
import { ICategoryItem } from "@/types/category";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Skeleton } from "../skeleton/skeleton";

interface TreatmentHeaderProps {
  category: ICategoryItem;
  isCategoryDetailLoading: boolean;
}

const TreatmentHeader = ({ category, isCategoryDetailLoading }: TreatmentHeaderProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  return (
    <section className="relative mx-auto flex w-full flex-col-reverse justify-start gap-8 px-4 sm:px-6 md:min-h-[600px] md:max-w-[1728px] md:gap-10 md:px-8 lg:h-[680px] lg:flex-row lg:px-16 xl:mt-4">
      {/* Content container */}
      <div className="z-[3] flex w-full flex-col justify-start gap-8 py-4 md:justify-between md:gap-0 md:py-6 lg:mt-4 lg:w-1/2 lg:py-8">
        {/* Text content */}
        <div className="flex w-1/2 flex-col justify-start gap-4 md:justify-center lg:h-full lg:w-full">
          {!isCategoryDetailLoading ? (
            <div className="">
              <span className="font-henju text-3xl font-medium text-[#4FA097] sm:text-5xl md:text-6xl lg:text-[5rem]">
                {category?.name?.split(" ")[0] + " "}
              </span>
              <span className="font-henju text-3xl font-medium text-black sm:text-5xl md:text-6xl lg:text-[5rem]">
                {category?.name?.split(" ").slice(1).join(" ")}
              </span>
            </div>
          ) : (
            <Skeleton className="h-20 w-1/2 rounded-md" />
          )}
          {!isCategoryDetailLoading ? (
            <div className="text-md w-full justify-start font-light text-[#1f1f1f] opacity-50 md:max-w-[400px] lg:max-w-[500px]">
              {category?.description}
            </div>
          ) : (
            <Skeleton className="h-10 w-2/3 rounded-md" />
          )}
          <Button
            variant="primary"
            withArrow
            className="w-fit"
            onClick={() => {
              router.push(`/questionnaire?category=${category?._id}`);
            }}
          >
            Get Started
          </Button>
        </div>
      </div>

      {/* Background decoration - responsive positioning */}
      <div className="absolute top-0 right-[62px] hidden h-[700px] w-[840px] rounded-[40px] bg-[#85b5a5]/10 lg:block" />

      {/* Image container */}
      <div className="absolute top-4 right-4 z-[2] h-full w-1/2 rounded-[40px] lg:top-12 lg:right-16 lg:block lg:h-[650px] lg:w-[617px]">
        <Image
          src={"/images/header/image5.jpg"}
          alt={"header image"}
          width={400}
          height={400}
          className="z-[-1] h-full w-full rounded-[20px] object-cover md:rounded-[40px]"
          priority
        />
      </div>
    </section>
  );
};

export default TreatmentHeader;
