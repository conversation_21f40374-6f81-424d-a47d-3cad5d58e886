import React from "react";
import ContactUsForm from "./contact-us-form";
import Image from "next/image";
import { MailIcon, PhoneCallIcon } from "lucide-react";

interface ContactUsSectionProps {
  variant?: "primary" | "secondary";
}

const ContactUsSection = ({ variant = "primary" }: ContactUsSectionProps) => {
  // Define the styles based on the variant
  const color = variant === "primary" ? "#85b5a5" : "#ffa068";
  const textStyle = variant === "primary" ? "text-[#85b5a5]" : "text-[#ffa068]";

  return (
    <section className="flex w-full max-w-[1205px] flex-col gap-8 px-4 md:flex-row md:gap-12 xl:px-0">
      <div className="flex w-full flex-col gap-6 rounded-[40px] bg-white px-8 py-8 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] md:gap-6 md:px-12 lg:w-1/2">
        <div className="flex flex-col gap-4">
          <div className="justify-start leading-0.5">
            <span
              className={`font-henju text-2xl font-normal sm:text-3xl md:text-4xl lg:text-5xl ${textStyle}`}
            >
              Contact{" "}
            </span>
            <span className="font-henju text-2xl font-normal text-black sm:text-3xl md:text-4xl lg:text-5xl">
              us.
            </span>
          </div>
          <div className="text-md sm:text-2md w-full font-light text-[#535353]">
            Have a question or feedback? We&apos;re here to help. Send us a message and we&apos;ll
            respond within 24 hours.
          </div>
        </div>
        <div className="h-0 w-full outline-[0.5px] outline-[#cdcdcd]" />
        <ContactUsForm variant={variant} />
      </div>
      <div className="flex h-full w-full flex-col gap-8 lg:w-1/2">
        <Image
          width={646}
          height={400}
          alt="contact us header image"
          className="h-2/3 w-full rounded-[40px]"
          src={
            variant === "primary"
              ? "/images/contact-us/image-green.png"
              : "/images/contact-us/image-orange.png"
          }
        />
        <div className="flex h-1/3 w-full flex-col gap-6 rounded-[40px] bg-white px-8 py-8 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] md:gap-6 md:px-12">
          <div className="inline-flex w-full items-center justify-start gap-7 border-b border-[#cdcdcd] p-5">
            <div className="size-6">
              <MailIcon className="size-5 stroke-1" color={color} />
            </div>
            <div className="flex w-full flex-col gap-1">
              <div className="justify-start text-xl font-normal text-[#cdcdcd]">Email</div>
              <div className="text-2md justify-start font-light text-black"></div>
              <EMAIL>
            </div>
          </div>
          <div className="inline-flex w-full items-center justify-start gap-7 p-5">
            <div className="size-6">
              <PhoneCallIcon className="size-5 stroke-1" color={color} />
            </div>
            <div className="flex w-full flex-col gap-1">
              <div className="justify-start text-xl font-normal text-[#cdcdcd]">Phone</div>
              <div className="text-2md justify-start font-light text-black"></div>
              +61 2 1234-5678
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactUsSection;
