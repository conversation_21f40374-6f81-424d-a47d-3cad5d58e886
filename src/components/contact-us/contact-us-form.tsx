"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { FormInput } from "@/components/ui/form-input";
import { useToast } from "@/context/toast-context";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import React, { useCallback, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

interface ContactUsFormProps {
  variant?: "primary" | "secondary";
}

// Define the validation schema using Zod
const contactFormSchema = z.object({
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  message: z.string().min(10, { message: "Message must be at least 10 characters" }),
  privacyPolicy: z.boolean().refine((val) => val === true, {
    message: "You must agree to our privacy policy",
  }),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

const ContactUsForm = ({ variant = "primary" }: ContactUsFormProps) => {
  const color = variant === "primary" ? "#85b5a5" : "#ffa068";
  const textStyle = variant === "primary" ? "text-[#85b5a5]" : "text-[#ffa068]";

  const { showToast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      message: "",
      privacyPolicy: false,
    },
  });

  // Handle form submission
  const onSubmit = useCallback(
    async (data: ContactFormData) => {
      setIsSubmitting(true);
      try {
        // Here you would typically send the data to your API
        console.log("Form data:", data);

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Show success message
        showToast("Your message has been sent successfully!", "success");

        // Reset form
        reset();
      } catch (error) {
        console.error("Error submitting form:", error);
        showToast("Failed to send message. Please try again.", "error");
      } finally {
        setIsSubmitting(false);
      }
    },
    [reset, showToast]
  );

  // Show first validation error as toast
  const onError = useCallback(() => {
    if (Object.keys(errors).length > 0) {
      const firstError = Object.values(errors)[0];
      const errorMessage = (firstError?.message as string) || "Please check the form for errors";
      showToast(errorMessage, "error");
    }
  }, [errors, showToast]);

  return (
    <form onSubmit={handleSubmit(onSubmit, onError)} className="w-full">
      <div className="flex flex-col gap-6">
        {/* Name Fields (First and Last) */}
        <div className="flex flex-col gap-6 sm:flex-row">
          <div className="w-full">
            <FormInput
              className="pl-6"
              placeholder="First name"
              error={errors.firstName}
              register={register("firstName")}
            />
          </div>
          <div className="w-full">
            <FormInput
              className="pl-6"
              placeholder="Last name"
              error={errors.lastName}
              register={register("lastName")}
            />
          </div>
        </div>

        {/* Email Field */}
        <div className="w-full">
          <FormInput
            className="pl-6"
            placeholder="Email"
            type="email"
            error={errors.email}
            register={register("email")}
          />
        </div>

        {/* Message Field */}
        <div className="relative min-h-[150px]">
          <textarea
            className={`block h-36 w-full rounded-2xl bg-white py-3 pr-3 pl-6 outline outline-offset-[-1px] lg:h-48 ${
              errors.message ? "outline-red-500" : "outline-[#ededed]"
            } text-2md font-light text-[#958e8e] placeholder-[#958e8e] transition duration-150 ease-in-out focus:outline-[#24A1B0]`}
            placeholder="Your message"
            {...register("message")}
          />
          {errors.message && (
            <p className="absolute -bottom-5 left-1 text-md text-red-500">
              {errors.message.message}
            </p>
          )}
        </div>

        {/* Privacy Policy Checkbox */}
        <div className="flex items-center gap-3">
          <Checkbox
            checked={watch("privacyPolicy")}
            onChange={(checked) => {
              register("privacyPolicy").onChange({
                target: { name: "privacyPolicy", value: checked },
              });
            }}
            error={!!errors.privacyPolicy}
            checkedIconClassName={variant === "primary" ? "bg-[#85b5a5]" : "bg-[#ffa068]"}
          />
          <div className="text-2md">
            <span className="font-normal text-black">I agree to your friendly </span>
            <Link href="/privacy-policy" className={`"font-normal underline ${textStyle}`}>
              privacy policy
            </Link>
          </div>
        </div>
        {errors.privacyPolicy && (
          <p className="text-md text-red-500">{errors.privacyPolicy.message}</p>
        )}

        {/* Submit Button */}
        <div className="">
          <Button
            type="submit"
            variant={variant === "primary" ? "primary" : "gradient"}
            isLoading={isSubmitting}
            withArrow
            className="w-full px-3 text-2md"
          >
            Send
          </Button>
        </div>
      </div>
    </form>
  );
};

export default React.memo(ContactUsForm);
