"use client";

import DropdownMenu from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { ChevronDown, FilterIcon, ListFilterIcon, Search } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";

interface ShopFilterV2Props {
  initialCategory?: string;
  initialFilter?: string;
  initialSort?: string;
  initialSearch?: string;
  onCategoryChange?: (category: string) => void;
  onFilterChange?: (filter: string) => void;
  onSortChange?: (sort: string) => void;
  onSearch?: (query: string) => void;
  className?: string;
}

export const ShopFilterV2: React.FC<ShopFilterV2Props> = ({
  initialCategory = "",
  initialFilter = "",
  initialSort = "",
  initialSearch = "",
  onCategoryChange,
  onFilterChange,
  onSortChange,
  onSearch,
  className,
}) => {
  const [selectedSort, setSelectedSort] = useState(initialSort);
  const [selectedFilter, setSelectedFilter] = useState(initialFilter);
  const [isVisible, setIsVisible] = useState(true);
  const filterRef = useRef<HTMLDivElement>(null);

  const sortOptions = useMemo(
    () => [
      { id: "price-asc", label: "Price: Low to High" },
      { id: "price-desc", label: "Price: High to Low" },
      { id: "newest", label: "Newest" },
    ],
    []
  );

  const filters = useMemo(
    () => [
      { id: "all", label: "All Products" },
      { id: "new", label: "New Arrivals" },
      { id: "popular", label: "Popular" },
    ],
    []
  );

  const handleFilterSelect = useCallback(
    (filter: string) => {
      setSelectedFilter(filter);
      if (onFilterChange) onFilterChange(filter);
    },
    [onFilterChange]
  );

  const handleSortSelect = useCallback(
    (sort: string) => {
      setSelectedSort(sort);
      if (onSortChange) onSortChange(sort);
    },
    [onSortChange]
  );

  const [searchQuery, setSearchQuery] = useState(initialSearch);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchQuery(value);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      if (onSearch) {
        searchTimeoutRef.current = setTimeout(() => {
          onSearch(value);
        }, 500); // 500ms debounce delay
      }
    },
    [onSearch]
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Set up intersection observer to hide filter when footer is visible
  useEffect(() => {
    // Find the footer element
    const footer = document.getElementById("footer");

    if (!footer) {
      console.warn('Footer element with id="footer" not found');
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        // When footer is intersecting (visible), hide the filter
        const [entry] = entries;
        setIsVisible(!entry.isIntersecting);
      },
      {
        // Start hiding when footer is 10% visible
        threshold: 0.1,
        // Start observing when footer is within 100px of viewport
        rootMargin: "100px",
      }
    );

    // Start observing the footer
    observer.observe(footer);

    // Cleanup function
    return () => {
      if (footer) {
        observer.unobserve(footer);
      }
    };
  }, []);

  return (
    <div
      ref={filterRef}
      className={cn(
        "fixed bottom-12 left-1/2 z-50 flex w-full -translate-x-1/2 transform items-center justify-center px-2 pb-2 transition-opacity duration-300 md:bottom-8 md:px-4 md:pb-4",
        isVisible ? "opacity-100" : "pointer-events-none opacity-0",
        className
      )}
    >
      <div className="relative h-auto w-full max-w-[260px] rounded-full md:h-16 md:max-w-[480px]">
        <div className="absolute top-0 left-0 h-auto w-full rounded-full bg-white px-3 py-2 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)] outline outline-[#ededed] md:h-16 md:px-9 md:py-3">
          <div className="flex flex-row items-center gap-2 md:gap-5">
            {/* Search Section */}
            <div className="group flex w-auto items-center justify-start gap-1 text-center text-md leading-normal font-normal text-[#535353] md:gap-2 md:text-2md">
              <Search className="h-4 w-4 stroke-1 text-[#535353] group-focus:stroke-2 md:h-6 md:w-6" />
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-12 bg-transparent text-md leading-normal font-normal text-[#535353] outline-none placeholder:text-[#535353] md:w-20 md:text-2md"
              />
            </div>

            {/* Divider */}
            <div className="h-6 w-0 outline-[0.50px] outline-[#a9a3a0] md:h-10" />

            {/* Sort By Section */}
            <DropdownMenu
              trigger={
                <div className="flex cursor-pointer items-center gap-1 md:gap-2">
                  <ListFilterIcon className="h-4 w-4 stroke-1 text-[#535353] md:h-6 md:w-6" />
                  <div className="justify-start text-center text-md leading-normal font-normal text-[#535353] md:text-2md">
                    Sort
                  </div>
                  <ChevronDown className="h-3 w-3 stroke-1 text-[#535353] md:h-5 md:w-5" />
                </div>
              }
              position="top"
              align="center"
              width="w-[160px] md:w-[200px]"
            >
              <div className="p-2 md:p-3">
                {sortOptions.map((sort, index) => (
                  <button
                    key={sort.id}
                    className={cn(
                      "w-full rounded-lg px-3 py-1.5 text-left text-[#535353] md:px-4 md:py-2",
                      "text-md md:text-2md",
                      "transition-all duration-200 hover:bg-gray-50",
                      "transform hover:translate-x-1",
                      selectedSort === sort.id && "bg-gray-50 font-medium"
                    )}
                    onClick={() => handleSortSelect(sort.id)}
                    style={{ transitionDelay: `${index * 50}ms` }}
                  >
                    {sort.label}
                  </button>
                ))}
              </div>
            </DropdownMenu>

            {/* Divider */}
            <div className="h-6 w-0 outline-[0.50px] outline-[#a9a3a0] md:h-10" />

            {/* Filter Section */}
            <DropdownMenu
              trigger={
                <div className="flex cursor-pointer items-center gap-1 md:gap-2">
                  <FilterIcon className="h-4 w-4 stroke-1 text-[#535353] md:h-6 md:w-6" />
                  <div className="justify-start text-center text-md leading-normal font-normal text-[#535353] md:text-2md">
                    Filter
                  </div>
                  <ChevronDown className="h-3 w-3 stroke-1 text-[#535353] md:h-5 md:w-5" />
                </div>
              }
              position="top"
              align="start"
              width="w-[160px] md:w-[200px]"
            >
              <div className="p-2 md:p-3">
                {filters.map((filter, index) => (
                  <button
                    key={filter.id}
                    className={cn(
                      "w-full rounded-lg px-3 py-1.5 text-left text-[#535353] md:px-4 md:py-2",
                      "text-md md:text-2md",
                      "transition-all duration-200 hover:bg-gray-50",
                      "transform hover:translate-x-1",
                      selectedFilter === filter.id && "bg-gray-50 font-medium"
                    )}
                    onClick={() => handleFilterSelect(filter.id)}
                    style={{ transitionDelay: `${index * 50}ms` }}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShopFilterV2;
