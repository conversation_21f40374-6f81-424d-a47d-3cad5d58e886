"use client";

import React, { useState, useRef, useCallback, useMemo } from "react";
import { cn } from "@/lib/utils";
import DropdownMenu from "@/components/ui/dropdown-menu";
import { LayoutGrid, Filter, ChevronDownIcon, Search, ChevronDown } from "lucide-react";

interface ShopFilterProps {
  initialCategory?: string;
  initialFilter?: string;
  initialSort?: string;
  initialSearch?: string;
  onCategoryChange?: (category: string) => void;
  onFilterChange?: (filter: string) => void;
  onSortChange?: (sort: string) => void;
  onSearch?: (query: string) => void;
  className?: string;
}

export const ShopFilter: React.FC<ShopFilterProps> = ({
  initialCategory = "",
  initialFilter = "",
  initialSort = "",
  initialSearch = "",
  onCategoryChange,
  onFilterChange,
  onSortChange,
  onSearch,
  className,
}) => {
  const [searchQuery, setSearchQuery] = useState(initialSearch);
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [selectedFilter, setSelectedFilter] = useState(initialFilter);
  const [selectedSort, setSelectedSort] = useState(initialSort);

  // We're not going to update internal state based on props changes
  // This was causing an infinite loop

  // Memoize dropdown options to prevent unnecessary re-renders
  const categories = useMemo(
    () => [
      { id: "skincare", label: "Skincare" },
      { id: "anti-aging", label: "Anti-aging" },
      { id: "weight-loss", label: "Weight Loss" },
    ],
    []
  );

  // Filter dropdown items
  const filters = useMemo(
    () => [
      { id: "all", label: "All Products" },
      { id: "new", label: "New Arrivals" },
      { id: "popular", label: "Popular" },
    ],
    []
  );

  // Sort dropdown items
  const sortOptions = useMemo(
    () => [
      { id: "price-asc", label: "Price: Low to High" },
      { id: "price-desc", label: "Price: High to Low" },
      { id: "newest", label: "Newest" },
    ],
    []
  );

  // Use useCallback to memoize event handlers
  const handleCategorySelect = useCallback(
    (category: string) => {
      setSelectedCategory(category);
      if (onCategoryChange) onCategoryChange(category);
    },
    [onCategoryChange]
  );

  const handleFilterSelect = useCallback(
    (filter: string) => {
      setSelectedFilter(filter);
      if (onFilterChange) onFilterChange(filter);
    },
    [onFilterChange]
  );

  const handleSortSelect = useCallback(
    (sort: string) => {
      setSelectedSort(sort);
      if (onSortChange) onSortChange(sort);
    },
    [onSortChange]
  );

  // Use a ref to store the timeout ID for debouncing
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchQuery(value);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      if (onSearch) {
        searchTimeoutRef.current = setTimeout(() => {
          onSearch(value);
        }, 500);
      }
    },
    [onSearch]
  );

  // We'll handle search directly in the input change handler instead of using an effect

  const handleSearchSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (onSearch) onSearch(searchQuery);
    },
    [onSearch, searchQuery]
  );

  return (
    <div className={cn("flex w-full flex-col gap-4 md:flex-row md:gap-6", className)}>
      {/* Category Dropdown */}
      <DropdownMenu
        trigger={
          <div className="flex h-16 w-full items-center rounded-[48.50px] bg-white px-6 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)] outline outline-[#ededed] md:w-44">
            <div className="flex flex-1 items-center gap-2">
              <LayoutGrid className="h-6 w-6 text-[#535353]" />
              <span className="text-center text-2md leading-normal font-normal text-[#535353]">
                {selectedCategory
                  ? categories.find((c) => c.id === selectedCategory)?.label
                  : "Category"}
              </span>
            </div>
            <ChevronDown className="h-5 w-5 text-[#535353]" />
          </div>
        }
        position="bottom"
        align="start"
        width="w-full md:w-44"
      >
        <div className="p-4">
          {categories.map((category) => (
            <button
              key={category.id}
              className={cn(
                "w-full rounded-lg px-4 py-2 text-left text-[#535353] transition-colors hover:bg-gray-50",
                selectedCategory === category.id && "bg-gray-50 font-medium"
              )}
              onClick={() => handleCategorySelect(category.id)}
            >
              {category.label}
            </button>
          ))}
        </div>
      </DropdownMenu>

      {/* Filter Dropdown */}
      <DropdownMenu
        trigger={
          <div className="flex h-16 w-full items-center rounded-[48.50px] bg-white px-6 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)] outline outline-[#ededed] md:w-44">
            <div className="flex flex-1 items-center gap-2">
              <Filter className="h-6 w-6 text-[#535353]" />
              <span className="text-center text-2md leading-normal font-normal text-[#535353]">
                {selectedFilter ? filters.find((f) => f.id === selectedFilter)?.label : "Filter"}
              </span>
            </div>
            <ChevronDown className="h-5 w-5 text-[#535353]" />
          </div>
        }
        position="bottom"
        align="start"
        width="w-full md:w-44"
      >
        <div className="p-4">
          {filters.map((filter) => (
            <button
              key={filter.id}
              className={cn(
                "w-full rounded-lg px-4 py-2 text-left text-[#535353] transition-colors hover:bg-gray-50",
                selectedFilter === filter.id && "bg-gray-50 font-medium"
              )}
              onClick={() => handleFilterSelect(filter.id)}
            >
              {filter.label}
            </button>
          ))}
        </div>
      </DropdownMenu>

      {/* Sort Dropdown */}
      <DropdownMenu
        trigger={
          <div className="flex h-16 w-full items-center rounded-[48.50px] bg-white px-6 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)] outline outline-[#ededed] md:w-44">
            <div className="flex flex-1 items-center gap-2">
              <ChevronDownIcon className="h-6 w-6 text-[#535353]" />
              <span className="text-center text-2md leading-normal font-normal text-[#535353]">
                {selectedSort ? sortOptions.find((s) => s.id === selectedSort)?.label : "Sort by"}
              </span>
            </div>
            <ChevronDown className="h-5 w-5 text-[#535353]" />
          </div>
        }
        position="bottom"
        align="start"
        width="w-full md:w-44"
      >
        <div className="p-4">
          {sortOptions.map((sort) => (
            <button
              key={sort.id}
              className={cn(
                "w-full rounded-lg px-4 py-2 text-left text-[#535353] transition-colors hover:bg-gray-50",
                selectedSort === sort.id && "bg-gray-50 font-medium"
              )}
              onClick={() => handleSortSelect(sort.id)}
            >
              {sort.label}
            </button>
          ))}
        </div>
      </DropdownMenu>

      {/* Search Input */}
      <form onSubmit={handleSearchSubmit} className="w-full md:flex-1">
        <div className="flex h-16 w-full items-center rounded-[48.50px] bg-white px-6 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)] outline outline-[#ededed]">
          <div className="flex flex-1 items-center gap-2">
            <Search className="h-6 w-6 text-[#535353]" />
            <input
              type="text"
              placeholder="Search"
              value={searchQuery}
              onChange={handleSearchChange}
              className="flex-1 border-none bg-transparent text-2md leading-normal font-normal text-[#535353] outline-none placeholder:text-[#535353]"
            />
          </div>
        </div>
      </form>
    </div>
  );
};

export default ShopFilter;
