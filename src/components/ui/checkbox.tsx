"use client";

import React from "react";
import MCQCheckIcon from "@/components/icons/mcq-check-icon";
import { cn } from "@/lib/utils";

interface CheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  error?: boolean;
  className?: string;
  checkedIconClassName?: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  error = false,
  className = "",
  checkedIconClassName = "",
}) => {
  return (
    <div
      className={`relative size-6 cursor-pointer overflow-hidden rounded-[8px] border-1 ${
        error ? "border-red-500" : "border-[#EDEDED]"
      } bg-white ${className}`}
      onClick={() => onChange(!checked)}
    >
      {checked && (
        <div
          className={cn(
            "bg-primary relative size-7 overflow-hidden rounded-[8px]",
            checkedIconClassName
          )}
        >
          <div className="absolute top-1 left-1">
            <MCQCheckIcon />
          </div>
        </div>
      )}
    </div>
  );
};
