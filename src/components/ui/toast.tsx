import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { X } from "lucide-react";
import React, { useEffect, useState } from "react";
import ToastIcon from "@/components/icons/toast-icon";

export type ToastType = "success" | "error" | "info" | "warning";

export interface ToastProps {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
  onClose: (id: string) => void;
  isExpanded?: boolean;
}

// Export type styles and colors for reuse
export const typeStyles = {
  success: "bg-[#f0fdf4] text-[#166534]",
  error: "bg-[#fff3f3] text-[#b42318]",
  info: "bg-[#e6f7f8] text-[#23a1b0]",
  warning: "bg-[#fffbeb] text-[#92400e]",
};

export const typeColors = {
  success: "#16a34a",
  error: "#D92D20",
  info: "#23a1b0",
  warning: "#f59e0b",
};

export const Toast: React.FC<ToastProps> = ({
  id,
  message,
  type = "info",
  duration = 5000,
  onClose,
  isExpanded = true,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Use a longer duration when expanded from collapsed state
  const effectiveDuration = isExpanded ? Math.max(duration, 8000) : duration;

  useEffect(() => {
    // Only auto-close if expanded and not being hovered
    if (isExpanded && !isHovered) {
      const timer = setTimeout(() => {
        onClose(id);
      }, effectiveDuration);

      return () => clearTimeout(timer);
    }
  }, [id, effectiveDuration, onClose, isExpanded, isHovered]);

  // Animation variants based on expanded state
  const variants = {
    initial: { opacity: 0, x: 30, scale: 0.9 },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: { duration: 0.2 },
    },
    exit: {
      opacity: 0,
      x: 30,
      scale: 0.9,
      transition: { duration: 0.1 },
    },
  };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={variants}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        "mb-3 flex w-[360px] items-start justify-between rounded-2xl p-4 shadow-md transition-all duration-200",
        typeStyles[type],
        isHovered && "translate-y-[-2px] shadow-lg"
      )}
    >
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0 pt-0.5">
          <ToastIcon size="xs" color={typeColors[type]} type={type} />
        </div>
        <div className="max-w-[260px] text-md leading-relaxed font-normal break-words">
          {message}
        </div>
      </div>
      <button
        onClick={() => onClose(id)}
        className="ml-4 inline-flex self-start text-gray-400 hover:text-gray-500 focus:outline-none"
        aria-label="Close notification"
      >
        <X className="h-4 w-4" />
      </button>
    </motion.div>
  );
};
