import { cn } from "@/lib/utils";
import React, { ReactNode } from "react";
import { FieldError } from "react-hook-form";

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: ReactNode;
  error?: FieldError;
  // @typescript-eslint/no-explicit-any
  register?: any; //@typescript-eslint/no-explicit-any
  rightIcon?: ReactNode;
}

export const FormInput = React.forwardRef<HTMLInputElement, FormInputProps>(
  ({ icon, error, className = "", register, rightIcon, ...props }, ref) => {
    return (
      <div className="relative min-h-[52px]">
        <div className="pointer-events-none absolute top-4 left-0 flex pl-8">{icon}</div>
        <input
          className={cn(
            `block h-12 w-full rounded-2xl bg-white pr-3 pl-[3.5rem] outline outline-offset-[-1px] outline-[#ededed] ${
              error ? "outline-red-500" : "outline-[#ededed]"
            } text-2md font-light text-[#958e8e] placeholder-[#958e8e] transition duration-150 ease-in-out focus:outline-[#24A1B0]`,
            className
          )}
          ref={ref}
          {...register}
          {...props}
        />
        {rightIcon}
        {error && <p className="absolute -bottom-3 left-1 text-md text-red-500">{error.message}</p>}
      </div>
    );
  }
);

FormInput.displayName = "FormInput";
