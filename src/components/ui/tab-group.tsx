"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { TabButton } from "./tab-button";

export interface TabItem {
  id: string;
  label: string;
}

interface TabGroupProps {
  tabs: TabItem[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
  tabClassName?: string;
  isLoading?: boolean;
  activeTabClassName?: string;
  inactiveTabClassName?: string;
}

export const TabGroup: React.FC<TabGroupProps> = ({
  tabs,
  activeTab: externalActiveTab,
  onTabChange,
  className,
  tabClassName,
  isLoading = false,
  activeTabClassName,
  inactiveTabClassName,
}) => {
  // Use internal state if no external state is provided
  const [internalActiveTab, setInternalActiveTab] = useState<string>(tabs[0]?.id || "");

  // Determine which active tab to use (external or internal)
  const activeTabId = externalActiveTab !== undefined ? externalActiveTab : internalActiveTab;

  const handleTabClick = (tabId: string) => {
    if (externalActiveTab === undefined) {
      setInternalActiveTab(tabId);
    }

    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  if (isLoading) {
    return (
      <div className={cn("flex flex-wrap gap-2", className)}>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="flex-1 min-w-[120px]">
            <div className="w-full h-14 bg-neutral-100 animate-pulse rounded-full"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {tabs.map((tab) => (
        <div key={tab.id} className="flex-1 min-w-[120px]">
          <TabButton
            label={tab.label}
            isActive={activeTabId === tab.id}
            onClick={() => handleTabClick(tab.id)}
            className={tabClassName}
            activeClassName={activeTabClassName}
            inactiveClassName={inactiveTabClassName}
          />
        </div>
      ))}
    </div>
  );
};

export default TabGroup;
