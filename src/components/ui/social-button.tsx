import React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface SocialButtonProps {
  icon: string;
  label: string;
  onClick?: () => void;
  className?: string;
}

export const SocialButton: React.FC<SocialButtonProps> = ({ icon, label, onClick, className }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={cn(
        "inline-flex w-[22rem] min-w-36 items-center justify-center rounded-full border border-[#eeeeee] bg-white px-4 py-3 pr-2 pl-5 text-2md font-light text-black hover:bg-gray-50 focus:ring-2 focus:ring-[#23A1B0] focus:ring-offset-2 focus:outline-none",
        className
      )}
    >
      <Image src={icon} alt={label} width={20} height={20} className="mr-2" />
      {label}
    </button>
  );
};
