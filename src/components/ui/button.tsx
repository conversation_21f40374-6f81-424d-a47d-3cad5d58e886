import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import React from "react";
import ArrowRightIcon from "../icons/arrow-right-icon";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "gradient";
  isLoading?: boolean;
  fullWidth?: boolean;
  withArrow?: boolean;
  icon?: React.ReactNode;
  iconColor?: string;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      className,
      variant = "primary",
      isLoading = false,
      fullWidth = false,
      withArrow = false,
      disabled,
      icon,
      iconColor,
      ...props
    },
    ref
  ) => {
    const baseStyles =
      "px-6 py-2.5 pr-2 pl-5 min-w-20 truncate font-quinn font-normal text-md rounded-none transition duration-300 disabled:opacity-70 disabled:cursor-not-allowed rounded-full cursor-pointer hover:scale-[101%]";

    const variants = {
      primary: "bg-[#4fa097] text-white hover:bg-[#3e8b7a]",
      secondary: "bg-white text-black border border-[#eeeeee] hover:bg-gray-50",
      outline:
        "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400",
      gradient: "bg-gradient-to-r from-[#ffb78d] to-[#ffa068] text-white",
    };

    return (
      <button
        ref={ref}
        disabled={isLoading || disabled}
        className={cn(
          baseStyles,
          variants[variant],
          fullWidth && "w-full",
          withArrow && "flex items-center justify-between gap-3.5 overflow-hidden",
          isLoading && "cursor-not-allowed justify-center",
          className
        )}
        {...props}
      >
        {isLoading ? (
          <Loader2 className="h-6 w-6 animate-spin justify-self-center text-center" />
        ) : (
          <>
            <div className={cn("flex-1", withArrow && "justify-start text-center")}>{children}</div>
            {withArrow && (
              <div className="relative size-6 rounded-2xl bg-white outline outline-offset-[-1px]">
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                  {icon ? (
                    icon
                  ) : (
                    <ArrowRightIcon
                      color={iconColor || (variant === "gradient" ? "#ffa068" : "#85b5a5")}
                    />
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </button>
    );
  }
);

Button.displayName = "Button";
