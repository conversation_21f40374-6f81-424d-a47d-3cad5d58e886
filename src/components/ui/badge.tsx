import { cn } from "@/lib/utils";
import React from "react";

interface BadgeProps {
  children: React.ReactNode;
  variant?: "primary" | "secondary" | "success" | "warning" | "error" | "pill";
  size?: "sm" | "md" | "lg";
  className?: string;
  withDot?: boolean;
  dotColor?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = "primary",
  size = "sm",
  className,
  withDot = false,
  dotColor,
}) => {
  const variants = {
    primary: "bg-[#f3edfa] text-[#82618e] outline-[#e7d9ef]",
    secondary: "bg-gray-100 text-gray-700 outline-gray-200",
    success: "bg-green-50 text-green-700 outline-green-100",
    warning: "bg-[#FEEFC6] text-[#B54708] outline-[#FEDF89]",
    error: "bg-red-50 text-red-700 outline-red-100",
    pill: "bg-[#eff8ff] text-[#175cd3] outline-[#b2ddff]",
  };

  const defaultDotColors = {
    primary: "#82618e",
    secondary: "#374151",
    success: "#16a34a",
    warning: "#f59e0b",
    error: "#ef4444",
    pill: "#2E90FA",
  };

  const sizes = {
    sm: "px-2 py-[0.5px] text-[10px]",
    md: "px-2.5 py-1 text-md",
    lg: "px-3 py-1.5 text-2md",
  };

  return (
    <span
      className={cn(
        "flex items-center gap-1 rounded-full font-normal outline-1 outline-offset-[-1px]",
        variant === "pill" && "inline-flex items-center gap-1.5",
        variants[variant],
        sizes[size],
        className
      )}
    >
      {withDot && (
        <div className="relative">
          <svg
            width="8"
            height="8"
            viewBox="0 0 8 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="4" cy="4" r="3" fill={dotColor || defaultDotColors[variant]} />
          </svg>
        </div>
      )}
      {children}
    </span>
  );
};

export default Badge;
