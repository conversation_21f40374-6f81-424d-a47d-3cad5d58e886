import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { ChevronUp, X } from "lucide-react";
import React from "react";
import ToastIcon from "@/components/icons/toast-icon";
import { ToastType, typeStyles, typeColors } from "./toast";

export interface CollapsedToastProps {
  count: number;
  types: ToastType[];
  onExpand: () => void;
  onClearAll: () => void;
}

export const CollapsedToast: React.FC<CollapsedToastProps> = ({
  count,
  types,
  onExpand,
  onClearAll,
}) => {
  // Determine the primary type to show (prioritize error > warning > info > success)
  const getPrimaryType = (): ToastType => {
    if (types.includes("error")) return "error";
    if (types.includes("warning")) return "warning";
    if (types.includes("info")) return "info";
    return "success";
  };

  const primaryType = getPrimaryType();

  // Animation variants
  const variants = {
    initial: { opacity: 0, x: 30, scale: 0.9 },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: { duration: 0.2 },
    },
    exit: {
      opacity: 0,
      x: 30,
      scale: 0.9,
      transition: { duration: 0.1 },
    },
  };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={variants}
      className={cn(
        "mb-3 flex w-[360px] items-center justify-between rounded-2xl p-4 shadow-md",
        typeStyles[primaryType]
      )}
    >
      <div
        className="flex cursor-pointer items-center gap-4"
        onClick={onExpand}
        aria-label="Expand notifications"
      >
        <div className="flex-shrink-0">
          <ToastIcon size="xs" color={typeColors[primaryType]} type={primaryType} />
        </div>
        <div className="text-md font-medium">
          {count === 1 ? "1 notification" : "Notifications"}
        </div>
        {count > 1 && (
          <div
            className="flex h-5 w-5 items-center justify-center rounded-full bg-white text-md font-bold"
            style={{ color: typeColors[primaryType] }}
          >
            {count}
          </div>
        )}
        <div className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-white">
          <ChevronUp className="h-3 w-3" style={{ color: typeColors[primaryType] }} />
        </div>
      </div>
      <button
        onClick={onClearAll}
        className="ml-4 inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"
        aria-label="Clear all notifications"
      >
        <X className="h-4 w-4" />
      </button>
    </motion.div>
  );
};
