"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface TabButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isActive?: boolean;
  label: string;
  activeClassName?: string;
  inactiveClassName?: string;
}

export const TabButton = React.forwardRef<HTMLButtonElement, TabButtonProps>(
  ({ className, isActive = false, label, activeClassName, inactiveClassName, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(
          "h-10 w-full cursor-pointer justify-start rounded-full text-center text-md leading-normal font-normal capitalize transition-all duration-200 sm:h-12 sm:text-2md md:h-14",
          isActive
            ? cn( "bg-gradient-to-r from-[#ffcfb3] to-[#ffd6bd] font-medium text-[#764e35]", activeClassName)
            : cn("bg-neutral-50 text-[#535353] hover:bg-neutral-100", inactiveClassName),
          className
        )}
        {...props}
      >
        {label}
      </button>
    );
  }
);

TabButton.displayName = "TabButton";
