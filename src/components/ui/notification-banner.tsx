import { AlertCircle, ArrowRight, LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface NotificationBannerProps {
  show?: boolean;
  message: string;
  actionLabel?: string;
  onAction?: () => void;
  variant?: "warning" | "error" | "success" | "info";
  icon?: LucideIcon;
}

const variantStyles = {
  warning: {
    bg: "bg-[#fff3f3]",
    text: "text-[#a65656]",
    hover: "hover:text-[#8a4747]",
  },
  error: {
    bg: "bg-red-50",
    text: "text-red-600",
    hover: "hover:text-red-700",
  },
  success: {
    bg: "bg-green-50",
    text: "text-green-600",
    hover: "hover:text-green-700",
  },
  info: {
    bg: "bg-blue-50",
    text: "text-blue-600",
    hover: "hover:text-blue-700",
  },
};

export const NotificationBanner = ({
  show = true,
  message,
  actionLabel,
  onAction,
  variant = "warning",
  icon: Icon = AlertCircle,
}: NotificationBannerProps) => {
  if (!show) return null;

  const styles = variantStyles[variant];

  return (
    <div className={cn("w-full py-1", styles.bg)}>
      <div className="mx-auto max-w-[1728px] px-4 lg:px-8">
        <div className="flex items-center justify-center gap-2">
          <div className={cn("flex items-center gap-2", styles.text)}>
            <Icon className="hidden h-5 w-5 sm:block" />
            <p className="text-2md sm:text-2md">{message}</p>
          </div>
          {actionLabel && (
            <button
              onClick={onAction}
              className={cn(
                "flex items-center gap-2 transition-colors duration-200",
                styles.text,
                styles.hover
              )}
            >
              <span className="hidden text-2md sm:block">{actionLabel}</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
