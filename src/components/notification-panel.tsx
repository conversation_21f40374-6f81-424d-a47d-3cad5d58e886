"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-media-query";
import DropdownMenu from "./ui/dropdown-menu";
import Dialog from "./ui/dialog";
import NotificationBellIcon from "./icons/notification-bell-icon";
import { Bell, Check, Clock, Package, User } from "lucide-react";

// Mock notification data - replace with actual API data
interface Notification {
  id: string;
  title: string;
  message: string;
  type: "order" | "appointment" | "general" | "system";
  timestamp: string;
  isRead: boolean;
  actionUrl?: string;
}

const mockNotifications: Notification[] = [
  {
    id: "1",
    title: "Order Shipped",
    message: "Your order #12345 has been shipped and is on its way.",
    type: "order",
    timestamp: "2 hours ago",
    isRead: false,
    actionUrl: "/my-orders/12345",
  },
  {
    id: "2",
    title: "Appointment Reminder",
    message: "Your consultation is scheduled for tomorrow at 2:00 PM.",
    type: "appointment",
    timestamp: "1 day ago",
    isRead: false,
    actionUrl: "/my-treatments",
  },
  {
    id: "3",
    title: "Welcome to Revolved",
    message: "Complete your profile to get personalized recommendations.",
    type: "general",
    timestamp: "3 days ago",
    isRead: true,
    actionUrl: "/profile",
  },
  {
    id: "4",
    title: "System Update",
    message: "We've updated our privacy policy. Please review the changes.",
    type: "system",
    timestamp: "1 week ago",
    isRead: true,
  },
];

const getNotificationIcon = (type: Notification["type"]) => {
  switch (type) {
    case "order":
      return Package;
    case "appointment":
      return Clock;
    case "general":
      return User;
    case "system":
      return Bell;
    default:
      return Bell;
  }
};

const getNotificationIconColor = (type: Notification["type"]) => {
  switch (type) {
    case "order":
      return "text-blue-600";
    case "appointment":
      return "text-green-600";
    case "general":
      return "text-primary";
    case "system":
      return "text-gray-600";
    default:
      return "text-gray-600";
  }
};

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onNotificationClick: (notification: Notification) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onNotificationClick,
}) => {
  const Icon = getNotificationIcon(notification.type);
  const iconColor = getNotificationIconColor(notification.type);

  return (
    <div
      className={cn(
        "group relative flex gap-3 p-4 transition-colors duration-200 hover:bg-gray-50",
        !notification.isRead && "bg-blue-50/30"
      )}
    >
      {/* Unread indicator */}
      {!notification.isRead && (
        <div className="bg-primary absolute top-6 left-2 size-2 rounded-full" />
      )}

      {/* Icon */}
      <div className={cn("flex-shrink-0 rounded-full bg-gray-100 p-2", iconColor)}>
        <Icon className="size-4" />
      </div>

      {/* Content */}
      <div className="min-w-0 flex-1">
        <div className="flex items-start justify-between gap-2">
          <div className="min-w-0 flex-1">
            <h4 className="font-dm-sans text-md truncate font-medium text-gray-900">
              {notification.title}
            </h4>
            <p className="font-dm-sans text-2md mt-1 line-clamp-2 text-gray-600">
              {notification.message}
            </p>
            <p className="font-dm-sans mt-2 text-sm text-gray-500">{notification.timestamp}</p>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
            {!notification.isRead && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onMarkAsRead(notification.id);
                }}
                className="hover:text-primary hover:bg-primary/10 rounded-full p-1 text-gray-400 transition-colors duration-200"
                title="Mark as read"
              >
                <Check className="size-4" />
              </button>
            )}
          </div>
        </div>

        {/* Action button */}
        {notification.actionUrl && (
          <button
            onClick={() => onNotificationClick(notification)}
            className="font-dm-sans text-primary hover:text-primary-hover mt-2 text-sm transition-colors duration-200"
          >
            View Details →
          </button>
        )}
      </div>
    </div>
  );
};

interface NotificationContentProps {
  onClose?: () => void;
  className?: string;
}

const NotificationContent: React.FC<NotificationContentProps> = ({ onClose, className }) => {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  const handleMarkAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id ? { ...notification, isRead: true } : notification
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications((prev) => prev.map((notification) => ({ ...notification, isRead: true })));
  };

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read when clicked
    handleMarkAsRead(notification.id);

    // Navigate to action URL if available
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }

    // Close the panel/dialog
    onClose?.();
  };

  return (
    <div className={cn("bg-white", className)}>
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-100 p-4">
        <div className="flex items-center gap-2">
          <h3 className="font-dm-sans text-lg font-medium text-gray-900">Notifications</h3>
          {unreadCount > 0 && (
            <span className="bg-primary flex size-5 items-center justify-center rounded-full text-xs font-medium text-white">
              {unreadCount}
            </span>
          )}
        </div>
        {unreadCount > 0 && (
          <button
            onClick={handleMarkAllAsRead}
            className="font-dm-sans text-primary hover:text-primary-hover text-sm transition-colors duration-200"
          >
            Mark all as read
          </button>
        )}
      </div>

      {/* Notifications List */}
      <div className="max-h-[400px] overflow-y-auto">
        {notifications.length > 0 ? (
          <div className="divide-y divide-gray-100">
            {notifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={handleMarkAsRead}
                onNotificationClick={handleNotificationClick}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center px-4 py-12">
            <Bell className="mb-4 size-12 text-gray-300" />
            <h4 className="font-dm-sans text-md mb-2 font-medium text-gray-900">
              No notifications
            </h4>
            <p className="font-dm-sans text-2md text-center text-gray-500">
              You're all caught up! We'll notify you when something new happens.
            </p>
          </div>
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="border-t border-gray-100 p-4">
          <button className="font-dm-sans text-primary hover:text-primary-hover w-full text-center text-sm transition-colors duration-200">
            View All Notifications
          </button>
        </div>
      )}
    </div>
  );
};

interface NotificationPanelProps {
  className?: string;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ className }) => {
  const isMobile = useIsMobile();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleTriggerClick = () => {
    if (isMobile) {
      setIsDialogOpen(true);
    }
  };

  const trigger = (
    <button className="mt-1 flex size-8 items-center justify-center rounded-full transition-colors duration-200 hover:bg-gray-50">
      <NotificationBellIcon className="size-6 text-black" />
    </button>
  );

  return (
    <>
      {/* For mobile: Dialog */}
      {isMobile ? (
        <>
          <div onClick={handleTriggerClick} className="cursor-pointer">
            {trigger}
          </div>
          <Dialog
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
            title="Notifications"
            className="h-full w-full max-w-full rounded-t-xl rounded-b-none sm:h-auto sm:rounded-xl 2xl:max-w-[500px]"
          >
            <NotificationContent
              onClose={() => setIsDialogOpen(false)}
              className="h-full max-h-[70vh] overflow-hidden"
            />
          </Dialog>
        </>
      ) : (
        /* For desktop: Dropdown */
        <DropdownMenu
          trigger={trigger}
          position="bottom"
          align="end"
          width="w-full md:w-[400px]"
          className={cn("", className)}
        >
          <NotificationContent />
        </DropdownMenu>
      )}
    </>
  );
};

export default NotificationPanel;
