"use client";

import Image from "next/image";
import { Copy } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";

interface TrackingCardProps {
  trackingNumber: string;
  deliveryCompanyLogo: string;
  className?: string;
  onTrackClick?: () => void;
  onCopyClick?: () => void;
}

const TrackingCard = ({
  trackingNumber,
  deliveryCompanyLogo,
  className,
  onTrackClick,
  onCopyClick,
}: TrackingCardProps) => {
  const handleCopyClick = () => {
    if (onCopyClick) {
      onCopyClick();
    } else {
      navigator.clipboard.writeText(trackingNumber);
      // You could add a toast notification here if you have a toast system
    }
  };

  return (
    <div
      className={cn(
        "inline-flex h-64 w-full max-w-[558px] flex-col items-center justify-center gap-4 rounded-2xl px-2.5 py-7 outline-1 outline-offset-[-1px] outline-[#83efe3]",
        className
      )}
    >
      <div>
        <div className="h-3 w-16 justify-center text-[10px] leading-normal font-normal text-black">
          Delivered by
        </div>

        <Image
          className="h-9 w-40 rounded-[5px]"
          src={deliveryCompanyLogo}
          alt="Delivery Company"
          width={156}
          height={35}
        />
      </div>

      <div className="inline-flex h-9 items-center justify-center gap-4 rounded-[80px] bg-neutral-50 py-2.5 pr-2.5 pl-5">
        <div className="justify-start text-2md font-light text-black">
          Tracking #{trackingNumber}
        </div>
        <button onClick={handleCopyClick} className="rounded-full bg-white p-1">
          <Copy className="size-4 stroke-[#FFA068] stroke-2" />
        </button>
      </div>

      <div className="justify-start px-4 text-2md font-light text-black">
        To track your order, please click the shipping company link below.
      </div>

      <Button
        variant="outline"
        onClick={onTrackClick}
        className="flex min-w-20 truncate border-[#85b5a5] py-5 pr-2 pl-5 text-2md text-[#85b5a5] hover:scale-105 hover:border-[#85b5a5] hover:bg-white md:py-2"
        withArrow
      >
        Track order
      </Button>
    </div>
  );
};

export default TrackingCard;
