import React from "react";
import { cn } from "@/lib/utils";

interface OrderProgressBarProps {
  currentStep?: number;
  steps?: string[];
  className?: string;
}

const OrderProgressBar: React.FC<OrderProgressBarProps> = ({
  currentStep = 2,
  steps = ["Preparing", "Out for Delivery", "Delivered"],
  className = "",
}) => {
  const getStepState = (stepIndex: number) => {
    if (stepIndex < currentStep - 1) return "completed";
    if (stepIndex === currentStep - 1) return "current";
    return "pending";
  };

  const getLineState = (stepIndex: number) => {
    if (stepIndex < currentStep - 1) return "completed";
    return "pending";
  };

  const renderStepCircle = (stepIndex: number) => {
    const state = getStepState(stepIndex);

    return (
      <div className="relative flex h-4 w-4 items-center justify-center md:h-5 md:w-5">
        <div
          className={cn(
            "h-2 w-2 rounded-full transition-all duration-300 md:h-2.5 md:w-2.5",
            state === "completed" && "bg-primary",
            state === "current" && "bg-primary scale-125",
            state === "pending" && "bg-black/45"
          )}
        />
      </div>
    );
  };

  const renderConnectingLine = (stepIndex: number) => {
    const state = getLineState(stepIndex);

    return (
      <div className="relative flex h-0.5 flex-1 items-center md:h-1">
        <div
          className={cn(
            "h-full w-full transition-all duration-300",
            state === "completed" && "bg-primary",
            state === "pending" && "bg-black/10"
          )}
        />
      </div>
    );
  };

  return (
    <div className={cn("flex w-full flex-col gap-3", className)}>
      {/* Progress line and circles container */}
      <div className="flex w-full items-center">
        {steps.map((_, index) => (
          <React.Fragment key={index}>
            {/* Left connecting line (hidden for first step) */}
            {index > 0 && <div className="flex flex-1">{renderConnectingLine(index - 1)}</div>}

            {/* Step circle */}
            <div className="relative z-10">{renderStepCircle(index)}</div>

            {/* Right connecting line (hidden for last step) */}
            {index < steps.length - 1 && (
              <div className="flex flex-1">{renderConnectingLine(index)}</div>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Step labels container */}
      <div className="flex w-full items-center justify-between">
        {steps.map((step, index) => (
          <div
            key={index}
            className={`flex flex-1 items-center px-1 ${index === 0 ? "justify-start" : index === 1 ? "justify-center" : "justify-end"}`}
          >
            <div
              className={cn(
                "text-center text-[9px] font-normal transition-colors duration-300 sm:text-[10px] md:text-xs",
                getStepState(index) !== "pending" ? "text-primary" : "text-black/40"
              )}
            >
              {step}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default OrderProgressBar;
