import React from "react";
import Image from "next/image";

interface ApproachCardProps {
  title: string;
  description: string;
  icon: string;
}

const ApproachCard: React.FC<ApproachCardProps> = ({ title, description, icon }) => {
  return (
    <div className="group relative w-full max-w-sm pt-16 transition-transform duration-300 hover:scale-105">
      {/* Card Background */}
      <div className="absolute top-16 left-0 h-full w-full rounded-lg bg-[#f3fdff] p-6" />

      {/* Icon Circle */}
      <div className="absolute top-8 left-1/2 flex size-16 -translate-x-1/2 items-center justify-center rounded-3xl bg-[#40bfaa]">
        {/* Icon SVGs */}
        <div className="relative size-8">
          <Image src={icon} alt="Folder icon" fill className="object-contain" />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 px-6 pt-12 pb-6">
        <h3 className="mb-4 text-xl font-light text-black md:text-2xl">{title}</h3>
        <p className="text-2md leading-normal font-light text-[#486817] md:text-2md">
          {description}
        </p>
      </div>
    </div>
  );
};

export default ApproachCard;
