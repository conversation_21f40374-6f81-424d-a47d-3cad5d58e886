"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React from "react";

const SolutionsJustForYouSection = () => {
  const router = useRouter();
  return (
    <section
      className="flex flex-col items-center px-4 sm:px-8 lg:px-16"
      id="solutions-just-for-you"
    >
      <div className="w-full max-w-[1440px]">
        <div className="mb-4 flex flex-col items-center lg:mb-16">
          <div className="text-center">
            <span className="font-henju text-2xl font-normal text-black sm:text-3xl md:text-4xl lg:text-5xl">
              Solutions{" "}
            </span>
            <span className="font-henju text-2xl font-normal text-[#0c7885] sm:text-3xl md:text-4xl lg:text-5xl">
              Just for You
            </span>
          </div>
          <p className="text-md sm:text-2md mt-[6px] w-full text-center font-light text-[#000000] sm:max-w-[400px] md:max-w-[500px]">
            Offer safe, high-quality, evidence-based treatment and care.
          </p>
        </div>

        <div className="flex w-full flex-col gap-8 lg:flex-row lg:justify-between lg:gap-16">
          {/* Medicine Card */}
          <div className="w-full lg:w-1/2">
            <div className="relative flex h-[240px] w-[88%] flex-col items-start justify-center rounded-[40px] bg-[#f0faff] p-4 md:h-[300px] md:max-w-[605px] xl:h-[450px] xl:max-w-[500px]">
              <div className="flex w-4/5 flex-col items-center justify-center gap-4 lg:w-full lg:gap-8">
                <div className="h-24 w-3/4 justify-start text-center lg:w-full">
                  <span className="font-henju text-2xl font-light text-black md:text-3xl lg:text-4xl">
                    Clinically{" "}
                  </span>
                  <span className="font-henju text-2xl font-light text-[#0c7885] md:text-3xl lg:text-4xl">
                    Tested and Proven{" "}
                  </span>
                  <span className="font-henju text-2xl font-light text-black md:text-3xl lg:text-4xl">
                    Ingredients
                  </span>
                </div>

                <div className="flex w-full flex-col items-center">
                  <Button
                    variant="primary"
                    className="bg-[#0c7885]"
                    withArrow
                    iconColor="#0c7885"
                    onClick={() => router.push("/questionnaire")}
                  >
                    Get Started
                  </Button>
                </div>
              </div>

              {/* <div className="absolute top-[467.16px] left-[517.82px] h-[27.84px] w-[175.40px] rounded-full bg-radial from-[#171e24] to-[#59738a] mix-blend-multiply blur-[5px]" /> */}
              <Image
                alt="Solutions Just for You"
                width={309.96}
                height={478.86}
                className="absolute top-4 -right-12 z-1 h-[200px] w-[100px] md:-right-16 md:h-[260px] md:w-[140px] lg:top-4 lg:-right-24 xl:-right-32 xl:h-[400px] xl:w-[200px]"
                src="/images/home/<USER>/image1.png"
              />
            </div>
          </div>
          {/* Doctor Card */}
          <div className="w-full lg:w-1/2">
            <div className="relative flex h-[240px] w-[88%] flex-col items-start justify-center rounded-[50px] bg-[#f0faff] p-4 md:h-[300px] md:max-w-[605px] xl:h-[450px] xl:max-w-[500px]">
              <div className="flex w-4/5 flex-col items-center justify-center gap-4 lg:w-full lg:gap-8">
                <div className="h-24 w-3/4 justify-start text-center lg:w-full">
                  <span className="font-henju text-2xl font-light text-black lg:text-4xl">
                    Prescribed by{" "}
                  </span>
                  <span className="font-henju text-2xl font-light text-[#0c7885] lg:text-4xl">
                    Licensed Providers
                  </span>
                </div>

                <div className="flex w-full flex-col items-center">
                  <Button
                    variant="primary"
                    className="bg-[#0c7885]"
                    withArrow
                    iconColor="#0c7885"
                    onClick={() => router.push("/questionnaire")}
                  >
                    Get Started
                  </Button>
                </div>
              </div>

              {/* <div className="absolute top-[467.16px] left-[517.82px] h-[27.84px] w-[175.40px] rounded-full bg-radial from-[#171e24] to-[#59738a] mix-blend-multiply blur-[5px]" /> */}
              <Image
                alt="Solutions Just for You"
                width={309.96}
                height={478.86}
                className="absolute -right-12 bottom-1 z-1 h-[220px] w-[140px] object-cover lg:top-4 lg:-right-24 xl:-right-32 xl:h-[400px] xl:w-[250px]"
                src="/images/home/<USER>/image2.png"
              />
            </div>
          </div>
        </div>
        <SecondImageSection />
      </div>
    </section>
  );
};

export default SolutionsJustForYouSection;

const SecondImageSection = () => {
  const router = useRouter();
  return (
    <div className="mt-4 flex flex-col items-center lg:mt-12">
      <div className="grid grid-cols-2 gap-4 lg:gap-8 xl:flex xl:flex-row">
        <Image
          alt="Solutions Just for You"
          width={300}
          height={475}
          className="h-[400px] rounded-[40px] object-cover object-center"
          src="/images/home/<USER>/image3.png"
        />
        <div>
          <Image
            alt="Solutions Just for You"
            width={300}
            height={475}
            className="h-[400px] rounded-[40px] object-cover object-center"
            src="/images/home/<USER>/image4.png"
          />
        </div>
        <div>
          <Image
            alt="Solutions Just for You"
            width={300}
            height={475}
            className="h-[400px] rounded-[40px] object-cover object-center"
            src="/images/home/<USER>/image5.png"
          />
        </div>

        <div className="flex w-full flex-col items-center justify-center gap-4 rounded-[30px] bg-[#f1fbff] p-4 md:w-80 lg:rounded-[40px] xl:h-[400px] xl:w-fit">
          <div className="justify-start text-center lg:w-full">
            <span className="font-henju text-2xl font-light text-black lg:text-4xl">Safe, </span>
            <span className="font-henju text-2xl font-light text-[#0c7885] lg:text-4xl">
              Regulated Pharmacies
            </span>
          </div>

          <Button
            variant="primary"
            className="bg-[#0c7885]"
            withArrow
            iconColor="#0c7885"
            onClick={() => router.push("/questionnaire")}
          >
            Get Started
          </Button>
        </div>
      </div>
    </div>
  );
};
