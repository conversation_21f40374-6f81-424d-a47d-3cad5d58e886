import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

const content = [
  {
    title: {
      part1: "Online",
      part2: "Assessment",
    },
    description: "Fill out a brief health questionnaire to get started.",
    colorStyle: "text-[#F2A472]",
    color: "#F2A472",
    icon: "/icons/home/<USER>/icon1.svg",
  },

  {
    title: {
      part1: "Prescriber",
      part2: "Consultation",
    },
    description: "A licensed provider reviews your info and issues a prescription if appropriate.",
    colorStyle: "text-[#85b5a5]",
    color: "#85b5a5",
    icon: "/icons/home/<USER>/icon2.svg",
  },
  {
    title: {
      part1: "Discreet",
      part2: "Delivery",
    },
    description: "Your treatment is delivered in plain, secure packaging to protect your privacy.",
    colorStyle: "text-[#0c7885]",
    color: "#0C7885",
    icon: "/icons/home/<USER>/icon3.svg",
  },
];

const HowItWorksSection = () => {
  return (
    <section
      className="flex w-full scroll-mt-20 flex-col items-center px-4 sm:px-8"
      id="how-it-works"
    >
      <div className="max-w-[1728px]">
        <div className="flex flex-col items-center">
          <div className="text-center">
            <span className="font-henju text-2xl font-normal text-black sm:text-3xl md:text-4xl lg:text-5xl">
              How It{" "}
            </span>
            <span className="font-henju text-2xl font-normal text-[#85B5A5] sm:text-3xl md:text-4xl lg:text-5xl">
              Works?
            </span>
          </div>
          <p className="text-md sm:text-2md m-[80px] mt-[6px] w-full text-center font-light text-[#000000] sm:max-w-[400px] md:max-w-[500px]">
            We&apos;ll be with you every step of the way on your journey to a better you.
            Assessment. Consultation. Delivery
          </p>
        </div>
        <div className="item-center flex w-full flex-col justify-center gap-3 md:flex-row md:gap-8 lg:gap-16">
          {content.map((item, index) => (
            <div
              key={index}
              className="relative flex aspect-[3] w-full flex-col justify-between rounded-[20px] bg-neutral-50 pt-6 pr-7 pb-8 pl-7 sm:aspect-[4] sm:w-full sm:pt-6 sm:pr-4 sm:pb-10 sm:pl-4 md:aspect-[4] md:w-1/3 md:rounded-[40px] md:pt-8 md:pr-6 md:pb-12 md:pl-6 lg:w-1/4"
            >
              <h1
                className={cn(
                  "font-dm-sans text-right text-[40px] leading-[100%] font-extralight tracking-normal",
                  item.colorStyle
                )}
              >
                {`0${index + 1}`}
              </h1>

              <div className="flex flex-col items-center justify-start gap-1.5 md:gap-4 lg:gap-6">
                <Image
                  src={item.icon}
                  alt={`${item.title.part1} ${item.title.part2}`}
                  width={100}
                  height={70}
                  className="size-14 max-w-none sm:size-12 md:size-12"
                />
                <div className="text-center">
                  <p className="font-henju text-xl font-light text-black sm:text-2xl md:text-2xl">
                    {item.title.part1}
                  </p>
                  <p
                    className={cn(
                      "font-henju text-xl font-light sm:text-2xl md:text-2xl",
                      item.colorStyle
                    )}
                  >
                    {item.title.part2}
                  </p>
                </div>
                <p className="text-2md md:text-2md text-center font-light text-black sm:text-[21px]">
                  {item.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
