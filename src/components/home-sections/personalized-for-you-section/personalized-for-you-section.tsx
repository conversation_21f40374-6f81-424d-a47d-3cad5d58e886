"use client";
import { useEffect, useRef, useState } from "react";
import gsap from "gsap";
import { Button } from "@/components/ui/button";
import Image from "next/image";

const contents = [
  {
    image: "/images/home/<USER>/image1.png",
    meditationKitImage: "/images/home/<USER>/image3.png",
    about: [
      {
        key: "Name",
        value: "<PERSON>",
      },
      {
        key: "Age",
        value: "32",
      },
      {
        key: "BMI",
        value: "29.5",
      },
    ],
    history: ["Ex-smoker", "Pre-diabetic"],
    second: [
      {
        key: "Goal",
        value: "Weight Loss",
      },
    ],
  },
  {
    image: "/images/home/<USER>/image2.png",
    meditationKitImage: "/images/home/<USER>/image4.png",
    about: [
      {
        key: "Name",
        value: "<PERSON><PERSON>",
      },
      {
        key: "Age",
        value: "40",
      },
    ],
    history: ["Ex-smoker"],
    second: [
      {
        key: "Goal",
        value: "Wrinkle & Aging",
      },
    ],
  },
];

const PersonalizedForYouSection = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Add refs for animation
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const kitImageRef = useRef<HTMLDivElement>(null);

  const animateChange = (nextIndex: number) => {
    const tl = gsap.timeline({
      onComplete: () => {
        setCurrentIndex(nextIndex);
        setIsAutoPlaying(false);
        setTimeout(() => setIsAutoPlaying(true), 100);
      },
    });
    // Animate image horizontally, others vertically
    tl.to(
      imageContainerRef.current,
      {
        opacity: 0,
        x: 80,
        duration: 0.35,
        ease: "power2.inOut",
      },
      0
    ).to(
      [contentContainerRef.current, kitImageRef.current],
      {
        opacity: 0,
        y: 40,
        duration: 0.35,
        stagger: 0.05,
        ease: "power2.inOut",
      },
      0
    );
  };

  useEffect(() => {
    // Animate in: image from left, others from below
    gsap.fromTo(
      imageContainerRef.current,
      { opacity: 0, x: -80 },
      { opacity: 1, x: 0, duration: 0.5, ease: "power2.out" }
    );
    gsap.fromTo(
      [contentContainerRef.current, kitImageRef.current],
      { opacity: 0, y: 40 },
      { opacity: 1, y: 0, duration: 0.5, stagger: 0.05, ease: "power2.out" }
    );
  }, [currentIndex]);

  const handleContentChange = (index: number) => {
    if (index === currentIndex) return;
    animateChange(index);
  };

  useEffect(() => {
    // Animate in when currentIndex changes
    gsap.fromTo(
      [imageContainerRef.current, contentContainerRef.current, kitImageRef.current],
      { opacity: 0, y: 40 },
      { opacity: 1, y: 0, duration: 0.5, stagger: 0.05, ease: "power2.out" }
    );
  }, [currentIndex]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isAutoPlaying) {
      interval = setInterval(() => {
        animateChange(currentIndex === contents.length - 1 ? 0 : currentIndex + 1);
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isAutoPlaying, currentIndex]);

  return (
    <section className="" id="solutions-just-for-you">
      <div className="mb-4 flex flex-col items-center lg:mb-16">
        <div className="text-center">
          <span className="font-henju text-2xl font-normal text-[#0c7885] sm:text-3xl md:text-4xl lg:text-5xl">
            Personalized Treatment{" "}
          </span>
          <span className="font-henju text-2xl font-normal text-black sm:text-3xl md:text-4xl lg:text-5xl">
            for You
          </span>
        </div>
        <p className="text-md sm:text-2md mt-[6px] w-full px-4 text-center font-light text-[#000000] sm:max-w-[400px] md:max-w-[500px]">
          Offer safe, high-quality, evidence-based treatment and care.
        </p>
      </div>

      <div className="relative flex h-[400px] w-full flex-col items-center justify-center gap-2 bg-[#0C7885] md:h-[600px] lg:h-[900px] lg:gap-4">
        <div
          className="relative h-[70%] w-[55%] rounded-[40px] lg:h-[80%] 2xl:w-[35%]"
          ref={imageContainerRef}
        >
          <Image
            src={contents[currentIndex]?.image}
            alt="Personalized for You"
            width={700}
            height={600}
            quality={100}
            className="h-full w-full rounded-[20px] object-cover object-top lg:rounded-[40px]"
          />

          <div
            className="absolute top-4 -left-20 flex flex-col justify-center gap-2 font-light transition-all duration-300 lg:top-40 lg:-left-52 lg:gap-4"
            ref={contentContainerRef}
          >
            <div className="w-[160px] rounded-[16px] bg-[#013845]/80 p-3 backdrop-blur-[25px] md:p-4 lg:w-[300px] lg:rounded-[20px]">
              <h2 className="text-xl text-[#8ce8c3]">About</h2>
              {contents[currentIndex]?.about.map((item, index) => (
                <div
                  key={index}
                  className="text-md flex items-center justify-between gap-0 text-white"
                >
                  <span className="text-white">{item.key}</span>
                  <span className="text-[#8ce8c3]">{item.value}</span>
                </div>
              ))}
            </div>
            <div className="w-[160px] rounded-[16px] bg-[#013845]/80 p-3 backdrop-blur-[25px] md:p-4 lg:w-[300px] lg:rounded-[20px]">
              {contents[currentIndex]?.second.map((item, index) => (
                <div
                  key={index}
                  className="text-md flex items-center justify-between gap-0 text-white"
                >
                  <span className="text-white">{item.key}</span>
                  <span className="text-[#8ce8c3]">{item.value}</span>
                </div>
              ))}
            </div>
            <div className="flex w-[160px] flex-row items-center justify-between rounded-[16px] bg-[#013845]/80 p-3 font-light backdrop-blur-[25px] md:p-4 lg:w-[300px] lg:rounded-[20px]">
              <p className="text-md text-white">History</p>
              <div className="text-md lg:text-md flex flex-col gap-0 text-white">
                {contents[currentIndex]?.history.map((item, index) => (
                  <div key={index} className="text-md lg:text-md gap-0 text-white">
                    <span className="text-[#8ce8c3]">{item}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div
            className="absolute top-8 -right-20 flex w-[160px] flex-col items-center justify-between rounded-[20px] bg-[#013845]/80 p-7 font-light backdrop-blur-[25px] md:-right-40 lg:top-40 lg:-right-60 lg:w-[300px] lg:rounded-[20px]"
            ref={kitImageRef}
          >
            <h2 className="pb-4 text-xl text-[#8ce8c3]">Medication Kit 1</h2>
            <div className="aspect-square w-full rounded-[16px] object-cover transition-all duration-500">
              <Image
                src={contents[currentIndex]?.meditationKitImage}
                alt="Medication Kit"
                width={100}
                height={100}
                quality={100}
                className="h-full w-full rounded-[16px] object-cover transition-all duration-500"
              />
            </div>
          </div>
        </div>
        <Button
          variant="primary"
          withArrow
          className="bg-[#013845]/80 backdrop-blur-[50px] hover:bg-[#013845]/90"
          iconColor="#013845"
        >
          Book a consultation
        </Button>
        <div className="inline-flex items-center justify-start gap-1 md:right-8 md:bottom-8 lg:right-12 lg:bottom-12">
          {contents.map((_, index) => (
            <div
              key={index}
              className={`h-2 cursor-pointer rounded-full bg-white transition-all duration-200 ${
                index === currentIndex ? `w-8` : `w-4 opacity-50`
              }`}
              onClick={() => handleContentChange(index)}
            />
          ))}
          {/* <div className="w-4 h-2 rounded-full opacity-50 bg-[#ffa068]" />} */}
          {/* <div className="w-8 h-2 rounded-full bg-[#ffa068]" /> */}
        </div>
      </div>
    </section>
  );
};

export default PersonalizedForYouSection;
