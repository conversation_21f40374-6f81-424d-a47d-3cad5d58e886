"use client";
import { Minus, Plus } from "lucide-react";
import React, { useState } from "react";

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: "How do I start my journey?",
    answer:
      "We know that starting something new can come with a lot of questions—and that’s completely normal. Whether you’re curious about how our treatments work, what to expect from your first session, or how to choose the right plan for you, you’re in the right place.This section covers the most common questions from our community, so you can feel more confident, informed, and ready to begin.If you still need help or can't find what you're looking for, don’t worry—we’re always just a message away. We know that starting something new can come with a lot of questions—and that’s completely normal. Whether you’re curious about how our treatments work, what to expect from your first session, or how to choose the right plan for you, you’re in the right place.This section covers the most common questions from our community, so you can feel more confident, informed, and ready to begin.",
  },
  {
    question: "Are your treatments safe?",
    answer:
      "Yes, all our treatments are clinically tested and approved. We work with certified professionals and use only the highest quality ingredients.",
  },
  {
    question: "Can I customize my treatment plan?",
    answer:
      "Absolutely! We believe in personalized care. Your treatment plan will be customized based on your specific needs and goals.",
  },
  {
    question: "How do I book a consultation?",
    answer:
      "You can book a consultation through our website, mobile app, or by calling our customer service team. We'll match you with the best specialist for your needs.",
  },
];

const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="">
      <div className="mx-auto flex w-full flex-col items-center px-4 lg:px-8">
        {/* Header */}
        <div className="mb-12 text-center">
          <h2 className="font-henju text-2xl font-normal text-[#85b5a5] sm:text-3xl md:text-4xl lg:text-5xl">
            FAQ
          </h2>
          <p className="mt-2 w-full text-center text-md font-light text-[#535353] sm:text-2md md:mt-4">
            Answers to the frequently asked questions
          </p>
        </div>

        {/* FAQ Items */}
        <div className="flex max-w-[1205px] flex-col gap-4">
          {faqData.map((faq, index) => (
            <div
              key={index}
              className="relative rounded-[28px] bg-[#85b5a5]/10 transition-all duration-500"
            >
              <button
                className="flex w-full items-center justify-between px-4 py-6 text-left md:px-12"
                onClick={() => toggleFAQ(index)}
                aria-expanded={openIndex === index}
              >
                <span className="font-henju pr-8 text-xl font-normal text-black md:text-2xl">
                  {faq.question}
                </span>
                <div className="relative size-8 flex-shrink-0">
                  {openIndex === index ? (
                    <Minus className="transform stroke-[#85B5A5] stroke-2 transition-transform duration-500" />
                  ) : (
                    <Plus
                      className={`transform stroke-[#85B5A5] stroke-2 transition-transform duration-500`}
                    />
                  )}
                </div>
              </button>
              <div
                className={`overflow-hidden transition-all duration-500 ${
                  openIndex === index ? "max-h-96" : "max-h-0"
                }`}
              >
                <p className="px-4 pb-6 text-2md font-light text-neutral-600 md:px-16">
                  {faq.answer}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
