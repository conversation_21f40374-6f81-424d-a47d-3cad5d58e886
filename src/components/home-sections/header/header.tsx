"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";

const slides = [
  {
    image: "/images/banner1.jpg",
    title: "Unlock Your Peak Vitality",
    description:
      "Discover our proven treatments that boost performance, support weight loss, slow ageing, and improve overall health.",
  },
  {
    image: "/images/banner1.jpg",
    title: "Transform Your Health",
    description:
      "Experience cutting-edge treatments designed to enhance your wellbeing and vitality.",
  },
  {
    image: "/images/banner1.jpg",
    title: "Expert Care, Better Results",
    description:
      "Our team of specialists is dedicated to helping you achieve optimal health outcomes.",
  },
];

const Header = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const handleSlideChange = (direction: "prev" | "next" | number) => {
    if (typeof direction === "number") {
      setCurrentSlide(direction);
    } else {
      setCurrentSlide((prev) => {
        if (direction === "prev") {
          return prev === 0 ? slides.length - 1 : prev - 1;
        } else {
          return prev === slides.length - 1 ? 0 : prev + 1;
        }
      });
    }
    // Reset auto-play timer when manually changing slides
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 100);
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isAutoPlaying) {
      interval = setInterval(() => {
        setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
      }, 5000); // Change slide every 5 seconds
    }

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  return (
    <div className="relative h-[400px] w-full sm:h-[500px] md:h-[600px] lg:h-[778px]">
      {/* Background Image */}
      <div className="relative h-full w-full">
        <Image
          src={slides[currentSlide].image}
          alt="Header background"
          fill
          className="object-cover transition-opacity duration-500"
          priority
        />
      </div>

      {/* Content Container */}
      <div className="absolute inset-0 container mx-auto px-4 md:px-8">
        <div className="relative flex h-full flex-col justify-center">
          {/* Text Content */}
          <div className="max-w-xl space-y-4 md:space-y-6">
            <h1 className="font-[syne] text-3xl font-normal text-black md:text-4xl lg:text-6xl">
              {slides[currentSlide].title}
            </h1>
            <p className="text-2md leading-normal font-normal text-black md:text-2md">
              {slides[currentSlide].description}
            </p>
            {/* CTA Button */}
            <button className="mt-6 bg-[#23a1b0] px-6 py-3 text-2md font-medium text-white transition-colors hover:bg-[#1c8a97]">
              Get Started
            </button>
          </div>

          {/* Slider Controls - Bottom */}
          <div className="absolute bottom-8 left-1/2 flex -translate-x-1/2 items-center gap-2">
            {/* Left Arrow */}
            <button
              onClick={() => handleSlideChange("prev")}
              className="flex size-4 items-center justify-center rounded-2xl bg-white transition-colors hover:bg-gray-100"
              aria-label="Previous slide"
            >
              <svg
                width="5"
                height="7"
                viewBox="0 0 5 7"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="scale-75"
              >
                <path
                  d="M3.875 6.25L1.125 3.5L3.875 0.75"
                  stroke="black"
                  strokeWidth="0.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>

            {/* Dots */}
            <div className="flex items-center gap-1 rounded-2xl bg-white px-3 py-1">
              {slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => handleSlideChange(index)}
                  className={`transition-all ${
                    currentSlide === index
                      ? "size-1.5 bg-[#40bfaa]"
                      : "size-[3px] bg-[#e9e9e9] hover:bg-[#d0d0d0]"
                  } rounded-full`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>

            {/* Right Arrow */}
            <button
              onClick={() => handleSlideChange("next")}
              className="flex size-4 items-center justify-center rounded-2xl bg-white transition-colors hover:bg-gray-100"
              aria-label="Next slide"
            >
              <svg
                width="5"
                height="7"
                viewBox="0 0 5 7"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="scale-75"
              >
                <path
                  d="M1.125 6.25L3.875 3.5L1.125 0.75"
                  stroke="black"
                  strokeWidth="0.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
