"use client";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";

const ribbonContents = [
  {
    title: "Free Express Delivery",
    icon: "/icons/header/slider/icon1.svg",
  },
  {
    title: "Discreet Packaging",
    icon: "/icons/header/slider/icon2.svg",
  },
  {
    title: "Personalized Treatment Plans - Tailored Just for You",
    icon: "/icons/header/slider/icon3.svg",
  },
  {
    title: "100% Online - No Appointments. No Waiting Rooms.",
    icon: "/icons/header/slider/icon4.svg",
  },
  {
    title: "Speak with Australian Clinicians",
    icon: "/icons/header/slider/icon5.svg",
  },
  {
    title: "Backed by Science. Built for You.",
    icon: "/icons/header/slider/icon6.svg",
  },
];

const HeaderRibbon = () => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isPaused, setIsPaused] = useState(false);
  const scrollSpeed = 0.5;
  const scrollPositionRef = useRef(0);

  const duplicateContents = [
    ...ribbonContents,
    ...ribbonContents,
    ...ribbonContents,
    ...ribbonContents,
  ];

  useEffect(() => {
    if (!scrollRef.current) return;

    let animationFrameId: number;

    const scroll = () => {
      if (!scrollRef.current || isPaused) return;

      scrollPositionRef.current += scrollSpeed;

      // Reset position when reaching end of first set
      if (scrollPositionRef.current >= scrollRef.current.scrollWidth / 4) {
        scrollPositionRef.current = 0;
      }

      scrollRef.current.scrollLeft = scrollPositionRef.current;
      animationFrameId = requestAnimationFrame(scroll);
    };

    // Start animation
    animationFrameId = requestAnimationFrame(scroll);

    // Cleanup
    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [isPaused, ribbonContents]);

  // Update scrollPositionRef when user manually scrolls
  const handleScroll = () => {
    if (scrollRef.current) {
      scrollPositionRef.current = scrollRef.current.scrollLeft;
    }
  };

  return (
    <div
      data-property-1="Default"
      className="scrollbar-hide flex h-12 w-full items-center gap-10 overflow-x-scroll bg-[#0c7885] sm:h-14 sm:gap-16 md:h-16 md:gap-20"
      ref={scrollRef}
      onScroll={handleScroll}
    >
      {duplicateContents.map((item, index) => (
        <div key={index} className="flex items-center justify-center gap-1 sm:gap-2">
          <Image
            src={item.icon}
            alt={item.title}
            width={24}
            height={24}
            className="h-4 w-4 sm:h-5 sm:w-5"
          />
          <span className="font-quinn text-md font-light whitespace-nowrap text-white sm:text-2md">
            {item.title}
          </span>
        </div>
      ))}
    </div>
  );
};

export default HeaderRibbon;
