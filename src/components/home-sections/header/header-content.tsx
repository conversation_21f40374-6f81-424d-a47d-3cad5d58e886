"use client";
import { useGetPublicCategories } from "@/api/category-service";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-media-query";
import { ArrowLeftIcon, ArrowRightIcon } from "lucide-react";
import Image from "next/image";
import { useEffect, useRef, useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { TreatmentContent } from "@/types/category";
import { useRouter } from "next/navigation";
import gsap from "gsap";

const HeaderContent = () => {
  const router = useRouter();

  const { categories, categoriesLoading, categoriesError, categoriesEmpty } =
    useGetPublicCategories({ page: 0, limit: 100 });

  // Generate treatment contents from API categories
  const treatmentContents: TreatmentContent[] = useMemo(() => {
    if (categoriesLoading || categoriesError || categoriesEmpty || !categories.length) {
      // Return empty array while loading or if no categories
      return [];
    }

    const mappedCategories = categories.map((category, index) => ({
      title: category.name,
      description: category.description,
      icon: `/images/header/icons/icon${index + 1}.svg`,
      image: `/images/header/images/image${(index % 5) + 1}.jpg`,
      href: `/treatments/${category.slug}`,
      qHref: `/questionnaire?category=${category._id}`,
    }));

    return [...mappedCategories, ...mappedCategories, ...mappedCategories];
  }, [categories, categoriesLoading, categoriesError, categoriesEmpty]);
  const isMobile = useIsMobile();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const prevIndexRef = useRef(currentIndex);

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = direction === "left" ? -280 : 280; // Approximate card width + gap

      container.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });

      // Update current index for visual indication
      if (direction === "right" && currentIndex < treatmentContents.length - 1) {
        setCurrentIndex((prev) => prev + 1);
      } else if (direction === "left" && currentIndex > 0) {
        setCurrentIndex((prev) => prev - 1);
      }
    }
  };

  // Add scroll event listener to update currentIndex based on scroll position
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (isMobile) {
        // For mobile, calculate which card is most visible
        const scrollLeft = container.scrollLeft;
        const cardWidth = 260 + 16; // card width + gap
        const visibleIndex = Math.round(scrollLeft / cardWidth);

        if (visibleIndex >= 0 && visibleIndex < treatmentContents.length) {
          setCurrentIndex(visibleIndex);
        }
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [isMobile, treatmentContents.length]);

  useEffect(() => {
    if (!imageContainerRef.current) return;
    // Animate in the new image
    gsap.fromTo(
      imageContainerRef.current,
      { y: 200, opacity: 0.5 },
      {
        y: 0,
        opacity: 1,
        duration: 0.4,
        ease: "power2.out",
      }
    );
    prevIndexRef.current = currentIndex;
  }, [currentIndex]);

  return (
    <section className="relative mx-auto flex min-h-[500px] w-full flex-col-reverse justify-start gap-8 px-4 sm:px-6 md:min-h-[600px] md:max-w-[1728px] md:gap-10 md:px-8 lg:h-[680px] lg:flex-row lg:px-16 xl:mt-4">
      {/* Content container */}
      <div className="z-[3] flex w-full flex-col justify-between gap-8 py-4 md:gap-0 md:py-6 lg:mt-4 lg:w-[50%] lg:py-8 2xl:w-[60%]">
        {/* Text content */}
        <div className="flex w-1/2 flex-col gap-4 lg:w-full">
          <div className="overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -50, opacity: 0 }}
                transition={{
                  duration: 0.6,
                  ease: [0.25, 0.46, 0.45, 0.94],
                }}
              >
                <span className="font-henju text-3xl font-medium text-[#4FA097] sm:text-5xl md:text-6xl lg:text-[5rem]">
                  {`${treatmentContents[currentIndex]?.title || "Personalized Treatment"} `}
                </span>
              </motion.div>
            </AnimatePresence>
            {/* <span className="font-henju text-3xl font-medium text-black sm:text-5xl md:text-6xl lg:text-[5rem]">
              that works for you.
            </span> */}
          </div>
          <div className="text-md w-full justify-start font-light text-[#1f1f1f] opacity-50 md:max-w-[400px] lg:max-w-[500px]">
            We believe everyone deserves to glow. Let us help you with our top quality technology
            and facilities.
          </div>
          <Button
            variant="primary"
            withArrow
            className="w-fit"
            onClick={() => {
              router.push(treatmentContents[currentIndex]?.qHref || "/questionnaire");
            }}
          >
            Get Started
          </Button>
        </div>

        {/* Treatment cards */}
        <div className="w-full">
          {/* Navigation Buttons - only show if we have content */}
          {treatmentContents.length > 0 && (
            <div className="mb-4 flex justify-end gap-3">
              <button
                onClick={() => scroll("left")}
                className="flex size-8 items-center justify-center rounded-full border border-[#e9e9e9] bg-white transition-colors hover:bg-gray-50 sm:size-10"
                aria-label="Previous treatment"
                disabled={currentIndex === 0}
              >
                <ArrowLeftIcon
                  className={`size-5 stroke-1 ${currentIndex === 0 ? "stroke-[#d1d1d1]" : "stroke-black"}`}
                />
              </button>
              <button
                onClick={() => scroll("right")}
                className="flex size-8 items-center justify-center rounded-full border border-[#e9e9e9] bg-white transition-colors hover:bg-gray-50 sm:size-10"
                aria-label="Next treatment"
                disabled={currentIndex === treatmentContents.length - 1}
              >
                <ArrowRightIcon
                  className={`size-5 stroke-1 ${currentIndex === treatmentContents.length - 1 ? "stroke-[#d1d1d1]" : "stroke-black"}`}
                />
              </button>
            </div>
          )}

          {/* Cards Container */}
          <div className="relative overflow-hidden">
            {categoriesLoading ? (
              // Loading skeleton state
              isMobile ? (
                <div className="scrollbar-hide flex snap-x snap-mandatory gap-4 overflow-x-auto p-1">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="w-[260px] flex-none snap-start">
                      <div className="max-h-lg inline-flex h-auto min-h-[180px] w-full flex-col items-start justify-start gap-4 rounded-2xl bg-white p-4 shadow-sm">
                        <div className="h-[32px] w-[32px] animate-pulse rounded-lg bg-neutral-200" />
                        <div className="flex w-full flex-col gap-2">
                          <div className="h-6 w-3/4 animate-pulse rounded-full bg-neutral-200" />
                          <div className="h-4 w-full animate-pulse rounded-full bg-neutral-200" />
                          <div className="h-4 w-2/3 animate-pulse rounded-full bg-neutral-200" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="scrollbar-hide flex snap-x snap-mandatory gap-6 overflow-x-auto pb-6">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="w-[240px] flex-none snap-start p-1 2xl:w-[200px]">
                      <div className="relative inline-flex h-52 w-full flex-col items-start justify-start gap-4 rounded-2xl bg-white p-4 shadow-sm md:gap-3 md:p-4 2xl:h-64">
                        <div className="h-[32px] w-[32px] animate-pulse rounded-lg bg-neutral-200 md:h-[52px] md:w-[52px]" />
                        <div className="flex w-full flex-col gap-2">
                          <div className="h-5 w-3/4 animate-pulse rounded-full bg-neutral-200 md:h-6" />
                          <div className="h-4 w-full animate-pulse rounded-full bg-neutral-200" />
                          <div className="h-4 w-2/3 animate-pulse rounded-full bg-neutral-200" />
                        </div>
                        <div className="absolute right-4 bottom-2">
                          <div className="h-5 w-5 animate-pulse rounded-full bg-neutral-200" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )
            ) : categoriesError || treatmentContents.length === 0 ? (
              // Error or empty state
              <div className="flex items-center justify-center py-8">
                <div className="text-md text-[#ababab]">No treatments available</div>
              </div>
            ) : isMobile ? (
              // Mobile: Horizontal scroller
              <div
                ref={scrollContainerRef}
                className="scrollbar-hide flex snap-x snap-mandatory gap-4 overflow-x-auto p-1"
              >
                {treatmentContents.map((content, index) => (
                  <div key={index} className="w-[260px] flex-none snap-start">
                    <div
                      className={`max-h-lg relative inline-flex h-auto min-h-[180px] w-full cursor-pointer flex-col items-start justify-start gap-4 rounded-2xl bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md ${
                        currentIndex === index ? "" : ""
                      }`}
                      onClick={() => {
                        setCurrentIndex(index);
                        if (scrollContainerRef.current) {
                          scrollContainerRef.current.scrollTo({
                            left: index * 276, // card width + gap
                            behavior: "smooth",
                          });
                        }
                      }}
                    >
                      <Image
                        src={content.icon}
                        alt={`${content.title} icon`}
                        width={40}
                        height={40}
                        className="h-[32px] w-[32px]"
                      />
                      <div className="flex flex-col gap-2">
                        <h3 className="font-henju text-2md text-[#1f1f1f]">{content.title}</h3>
                        <p className="line-clamp-5 text-xs font-light text-[#ababab]">
                          {content.description}
                        </p>
                      </div>
                      <div className="absolute right-4 bottom-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(content.href);
                          }}
                          className="flex items-center gap-2 text-[#4FA097] transition-colors hover:scale-105 hover:text-[#3a7f6c]"
                        >
                          <ArrowRightIcon className="size-5 stroke-1 text-[#4FA097] transition-colors hover:text-[#3a7f6c]" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Tablet/Desktop: Horizontal scroll
              <div
                ref={scrollContainerRef}
                className="scrollbar-hide flex snap-x snap-mandatory gap-6 overflow-x-auto pb-6"
              >
                {treatmentContents.map((content, index) => (
                  <div key={index} className="w-[240px] flex-none snap-start p-1 2xl:w-[200px]">
                    <div
                      className={`relative inline-flex h-52 w-full cursor-pointer flex-col items-start justify-start gap-4 rounded-2xl bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md md:gap-3 md:p-4 2xl:h-64 ${currentIndex === index ? "" : ""} `}
                      onClick={() => setCurrentIndex(index)}
                    >
                      <Image
                        src={content.icon}
                        alt={`${content.title} icon`}
                        width={40}
                        height={40}
                        className="h-[32px] w-[32px] md:h-[52px] md:w-[52px]"
                      />
                      <div className="flex flex-col gap-2">
                        <h3 className="font-henju text-lg text-[#1f1f1f] md:text-lg">
                          {content.title}
                        </h3>
                        <p className="text-md line-clamp-3 font-light text-[#ababab] 2xl:line-clamp-5">
                          {content.description}
                        </p>
                      </div>
                      <div className="absolute right-4 bottom-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(content.href);
                          }}
                          className="flex items-center gap-2 text-[#4FA097] transition-colors hover:scale-105 hover:text-[#3a7f6c]"
                        >
                          <ArrowRightIcon className="size-5 stroke-1 text-[#4FA097] transition-colors hover:text-[#3a7f6c]" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Dots indicator */}
          {/* <div className="mt-4 flex justify-center gap-2 lg:hidden">
            {treatmentContents.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentIndex(index);
                  if (scrollContainerRef.current) {
                    scrollContainerRef.current.scrollTo({
                      left: index * (isMobile ? 276 : 286), // card width + gap
                      behavior: "smooth",
                    });
                  }
                }}
                className={`size-2.5 rounded-full transition-all duration-300 sm:size-3 ${
                  index === currentIndex
                    ? "scale-110 transform bg-[#85b5a5]"
                    : "bg-gray-200 hover:bg-gray-300"
                }`}
                aria-label={`Go to treatment ${index + 1}`}
              />
            ))}
          </div> */}
        </div>
      </div>

      {/* Background decoration - responsive positioning */}
      <div className="absolute top-0 right-[62px] hidden h-[700px] w-[840px] rounded-[40px] bg-[#85b5a5]/10 lg:block" />

      {/* Image container */}
      <div className="absolute top-4 right-4 z-[2] h-1/2 w-1/2 overflow-hidden rounded-[20px] md:rounded-[40px] lg:top-12 lg:right-16 lg:block lg:h-[650px] lg:w-[617px]">
        <div ref={imageContainerRef} className="h-full w-full">
          <Image
            src={treatmentContents[currentIndex]?.image || "/images/header/image5.jpg"}
            alt={"header image"}
            width={400}
            height={400}
            className="z-[-1] h-full w-full rounded-[20px] object-cover md:rounded-[40px]"
            priority
          />
        </div>
      </div>
    </section>
  );
};

export default HeaderContent;
