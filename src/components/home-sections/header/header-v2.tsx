"use client";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { useState, useEffect } from "react";
import { Button } from "../../ui/button";

const slides = [
  {
    id: 1,
    title: "Weight loss",
    subtitle: "Treatment",
    description: "Customized just for you.",
    imagePath: "/images/header/image1.png",
  },
  {
    id: 2,
    title: "Anti-Aging",
    subtitle: "Solutions",
    description: "Personalized care for your skin.",
    imagePath: "/images/header/image2.png",
  },
  {
    id: 3,
    title: "Wellness",
    subtitle: "Programs",
    description: "Transform your lifestyle.",
    imagePath: "/images/header/image3.png",
  },
];

const HeaderV2 = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);

  useEffect(() => {
    if (!isAutoPlay) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlay]);

  return (
    <div className="relative flex h-96 w-full justify-between overflow-hidden rounded-[50px] bg-neutral-50 py-6 pr-10 pl-20">
      {/* Text Content */}
      <AnimatePresence mode="wait">
        <motion.div key={currentSlide} className="relative flex flex-col justify-center gap-4">
          <div className="flex items-end gap-3">
            <motion.h2
              className="text-6xl font-normal text-[#24a1b0]"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
            >
              {slides[currentSlide].title}
            </motion.h2>

            <p className="text-2xl font-normal text-black">Treatment</p>
          </div>

          <h1 className="text-6xl font-normal text-black">Customized just for you.</h1>

          {/* Get Started Button */}
          <Button variant="primary" withArrow className="min-w-32 truncate py-2 pr-2 pl-5 text-2md">
            Get Started
          </Button>
        </motion.div>
      </AnimatePresence>

      {/* Image Section */}
      <div className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="size-96"
          >
            <Image
              src={slides[currentSlide].imagePath}
              alt={slides[currentSlide].title}
              fill
              className="rounded-[50px] object-cover"
              priority
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </motion.div>
        </AnimatePresence>
        {/* Navigation Controls */}
        <div className="absolute bottom-4 left-1/2 flex -translate-1/2 transform items-center gap-4">
          {/* Previous Button */}
          <button
            onClick={() => setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)}
            className="flex size-[17px] items-center justify-center rounded-full bg-white"
          >
            <svg
              width="12"
              height="11"
              viewBox="0 0 12 11"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7.375 8.25L4.625 5.5L7.375 2.75"
                stroke="black"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          {/* Dots */}
          <div className="flex h-4 w-12 items-center justify-center gap-1.5 rounded-2xl bg-white">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`size-1.5 rounded-full ${
                  index === currentSlide ? "bg-[#ffa068]" : "bg-[#fff2ed]"
                }`}
              />
            ))}
          </div>

          {/* Next Button */}
          <button
            onClick={() => setCurrentSlide((prev) => (prev + 1) % slides.length)}
            className="flex size-[17px] items-center justify-center rounded-full bg-white"
          >
            <svg
              width="12"
              height="11"
              viewBox="0 0 12 11"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.625 8.25L7.375 5.5L4.625 2.75"
                stroke="black"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default HeaderV2;
