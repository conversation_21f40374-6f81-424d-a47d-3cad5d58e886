"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { CheckIcon } from "@/components/icons/check-icon";

const contents = [
  {
    title: "Unlock the Beauty of <PERSON><PERSON><PERSON>, <PERSON>.",
    icon: "/icons/logo3.svg",
    color: "#000000",
    colorStyle: "text-[#ffa068]",
    bgStyle: "bg-[#ffa068]",

    bgHoverStyle: "bg-[#4fa097]/50",
    buttonVariant: "gradient",
    gradientStyle: "from-[#ffeee4] via-[#fff4ee] to-[#fffaf7]/0",
    titleColor: "#000000",
    titleStyles: "text-black",
    description: "Reveal Your Hair's True Potential with Enhanced Volume and Density.",
    imageUrl: "/images/home/<USER>",
    subtitle: {
      part1: "Data-Driven",
      part2: "Hair Treatments",
    },
    subDescription:
      "We analyze your unique hair and scalp profile to pinpoint hair loss and craft a targeted treatment plan.",
    contents: [
      "No two heads of hair are the same, we design your personalized hair care plan",
      "Goodbye to generic advice.",
      "No more neglecting your tresses.",
    ],
  },
  {
    title: "The Power of True Recovery and Renewal.",
    icon: "/icons/logo4.svg",
    color: "#4fa097",
    colorStyle: "text-[#4fa097]",
    bgStyle: "bg-[#4fa097]",
    bgHoverStyle: "bg-[#ffa068]/50",
    buttonVariant: "primary",
    gradientStyle: "from-[#eaefed] via-[#eaefed] to-[#fffaf7]/0",
    titleColor: "#ffffff",
    titleStyles: "text-white",
    description: "Rediscover Your Strength with Targeted Repair and Whole-Body Recovery.",
    imageUrl: "/images/home/<USER>",
    subtitle: {
      part1: "Data-Driven",
      part2: "Repair and Recovery ",
    },
    subDescription:
      "We assess your body's unique needs to support recovery, restore balance, and accelerate healing with a plan that’s made just for you.",
    contents: [
      "No two recovery journeys are the same — your treatment is personalized from the start.",
      "Say goodbye to one-size-fits-all solutions.",
      "No more pushing through burnout or ignoring what your body needs.",
    ],
  },
];

const DataDrivenSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const handleSlideChange = (direction: "prev" | "next" | number) => {
    if (typeof direction === "number") {
      setCurrentSlide(direction);
    } else {
      setCurrentSlide((prev) => {
        if (direction === "prev") {
          return prev === 0 ? contents.length - 1 : prev - 1;
        } else {
          return prev === contents.length - 1 ? 0 : prev + 1;
        }
      });
    }
    // Reset auto-play timer when manually changing slides
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 100);
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;

    console.log("slide number", currentSlide);
    if (isAutoPlaying) {
      interval = setInterval(() => {
        setCurrentSlide((prev) => (prev === contents.length - 1 ? 0 : prev + 1));
      }, 5000); // Change slide every 5 seconds
    }

    return () => clearInterval(interval);
  }, [isAutoPlaying]);
  return (
    <section className="flex flex-col items-center bg-cover bg-fixed bg-center bg-no-repeat px-4 sm:px-8 md:px-12 lg:px-16">
      <div
        className={`relative h-[800px] w-full max-w-[1728px] overflow-hidden rounded-[30px] bg-cover bg-center transition-all duration-500 md:h-[800px] lg:h-[1200px] lg:rounded-[40px]`}
        style={{
          backgroundImage: `url(${contents[currentSlide].imageUrl})`,
        }}
      >
        <div className="flex h-full w-full flex-col items-center justify-start gap-2 px-8 py-4 md:gap-4 md:py-8">
          <h1
            className={`font-henju text-xl font-normal md:text-4xl lg:text-5xl ${contents[currentSlide].titleStyles}`}
          >
            {contents[currentSlide].title}
          </h1>
          <p className={`text-md md:text-md font-light ${contents[currentSlide].titleStyles}`}>
            {contents[currentSlide].description}
          </p>
          <button className="text-md md:text-2md h-8 w-32 rounded-full bg-white/50 font-light text-black outline-1 outline-white backdrop-blur-[10px] md:h-12 md:w-52">
            Get Started
          </button>
        </div>
        <div className="absolute bottom-0 h-1/2 w-full">
          <div
            className={`from 14% to 80% absolute bottom-0 left-0 h-full w-full bg-linear-270 bg-gradient-to-t ${contents[currentSlide].gradientStyle}`}
          />
          <div className="relative z-[2] flex h-full w-full flex-col items-start justify-end gap-2 px-8 py-4 md:w-1/2 md:gap-5 md:px-12 md:py-12 lg:px-16">
            <div className="flex flex-col gap-0 md:gap-3">
              <h2>
                <span className="font-henju text-lg font-normal text-black md:text-2xl lg:text-3xl">
                  {contents[currentSlide].subtitle.part1}{" "}
                </span>
                <span
                  className={`font-henju font-normal ${contents[currentSlide].colorStyle} text-lg md:text-2xl lg:text-3xl`}
                >
                  {contents[currentSlide].subtitle.part2}
                </span>
              </h2>
              <p className="text-md md:text-md font-light text-black">
                {contents[currentSlide].subDescription}
              </p>
            </div>
            <div className="flex flex-col gap-2 md:gap-4">
              {contents[currentSlide].contents.map((content, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div
                    className={`flex size-2.5 items-center justify-center rounded-full ${contents[currentSlide].bgStyle}`}
                  >
                    <CheckIcon className="size-1.5" color="#fff" />
                  </div>
                  <p key={index} className="text-md md:text-md font-light text-black">
                    {content}
                  </p>
                </div>
              ))}
            </div>
            <div className="mt-4 flex gap-2">
              <Button
                variant={contents[currentSlide].buttonVariant as "primary" | "gradient"}
                withArrow
                className="w-full min-w-60 md:min-w-32"
              >
                Select your treatment plan
              </Button>
            </div>
          </div>
          <div data-svg-wrapper className="absolute right-4 -bottom-6 md:right-10 md:-bottom-12">
            <Image
              src={contents[currentSlide].icon}
              alt={"revolved icon"}
              width={100}
              height={100}
              className="h-[140px] w-[140px] md:h-[280px] md:w-[280px] lg:h-[400px] lg:w-[400px]"
            />
          </div>
        </div>
        <div className="absolute right-4 bottom-4 inline-flex items-center justify-start gap-1 p-2.5 md:right-8 md:bottom-8 lg:right-12 lg:bottom-12">
          {contents.map((_, index) => (
            <div
              key={index}
              className={`h-2 cursor-pointer rounded-full transition-all duration-200 ${
                index === currentSlide
                  ? `w-8 ${contents[index].bgStyle}`
                  : `w-4 ${contents[index].bgHoverStyle}`
              }`}
              onClick={() => handleSlideChange(index)}
            />
          ))}
          {/* <div className="w-4 h-2 rounded-full opacity-50 bg-[#ffa068]" />} */}
          {/* <div className="w-8 h-2 rounded-full bg-[#ffa068]" /> */}
        </div>
      </div>
    </section>
  );
};

export default DataDrivenSection;
