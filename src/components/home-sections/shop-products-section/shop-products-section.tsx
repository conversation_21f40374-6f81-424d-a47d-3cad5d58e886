"use client";
import { useGetPublicProductCategories } from "@/api/category-service";
import { TabButton } from "@/components/ui/tab-button";
import { TabItem } from "@/components/ui/tab-group";
import React, { useCallback, useMemo, useRef } from "react";
import ProductsHorizontalScroller from "./products-horizontal-scroller";

const ShopProductsSection = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>("all");
  const {
    productCategories,
    productCategoriesLoading,
    productCategoriesError,
    productCategoriesEmpty,
  } = useGetPublicProductCategories({ page: 0, limit: 100 });

  // Transform categories into tab format
  const tabs: TabItem[] = useMemo(() => {
    if (productCategoriesEmpty || productCategoriesEmpty || !productCategories.length) {
      // Fallback tabs if categories can't be loaded
      return [];
    }

    const mappedCategories = productCategories.map((category) => ({
      id: category._id || category.id,
      label: category.name,
    }));

    return [{ id: "all", label: "All" }, ...mappedCategories];
  }, [productCategories, productCategoriesEmpty, productCategoriesError]);

  const handleTabChange = useCallback((tabId: string) => {
    // if (tabId === filterCriteria.activeTab) {
    //   setFilterCriteria((prev) => ({
    //     ...prev,
    //     activeTab: "",
    //     category: "",
    //   }));
    //   return;
    // }

    setSelectedCategory(tabId);
  }, []);

  // Render category tabs with proper error handling
  const renderCategoryTabs = () => {
    if (productCategoriesLoading) {
      return (
        <div className={"flex flex-wrap gap-4"}>
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="min-w-[120px] flex-1">
              <div className="h-14 w-full animate-pulse rounded-full bg-neutral-100"></div>
            </div>
          ))}
        </div>
      );
    }

    if (productCategoriesError) {
      return (
        <div className="flex w-full justify-center">
          <div className="text-red-500">
            Error loading categories: {productCategoriesError.message}
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full px-4 sm:px-6 lg:px-16">
        <div className="flex items-center">
          <div
            ref={scrollContainerRef}
            className="scrollbar-hide flex snap-x snap-mandatory gap-6 overflow-x-auto px-8 py-4"
          >
            {tabs.map((tab) => (
              <div key={tab.id} className="flex-none snap-start">
                <TabButton
                  label={tab.label}
                  isActive={selectedCategory === tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className="min-w-[120px] px-4"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <section className="flex w-full flex-col items-center px-4 sm:px-8 md:px-12 lg:px-16">
      <div className="w-full max-w-[1728px]">
        <div className="mb-4 flex w-full flex-col items-center">
          <div className="text-center">
            <span className="font-henju text-2xl font-normal text-black sm:text-3xl md:text-4xl lg:text-5xl">
              Shop{" "}
            </span>
            <span className="font-henju text-2xl font-normal text-[#f2a472] sm:text-3xl md:text-4xl lg:text-5xl">
              Products
            </span>
          </div>
          <p className="text-md sm:text-2md mt-2 w-full text-center font-light text-[#535353] sm:mt-4 sm:max-w-[400px] md:max-w-[600px]">
            Discover science-backed beauty essentials—curated to enhance your skin, hair, and
            overall glow.
          </p>
        </div>
        <div>{renderCategoryTabs()}</div>
        <div className="mt-4">
          <ProductsHorizontalScroller category={selectedCategory || "all"} />
        </div>
      </div>
    </section>
  );
};

export default ShopProductsSection;
