"use client";

import { useGetInfiniteProducts } from "@/api/product-service";
import { ProductCard } from "@/components/product-card";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface ProductsHorizontalScrollerProps {
  category: string;
}

export default function ProductsHorizontalScroller({ category }: ProductsHorizontalScrollerProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isNearEnd, setIsNearEnd] = useState(false);

  const { productsList, productsLoading, productsError, isLoadingMore, hasMore, loadMore } =
    useGetInfiniteProducts({
      category: category === "all" ? "" : category,
      limit: 6,
    });

  // Check if user has scrolled near the end to load more data
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollLeft, scrollWidth, clientWidth } = container;
      // Consider "near end" when less than 20% of the container width remains
      const isNearEndNow = scrollLeft + clientWidth >= scrollWidth * 0.8;

      if (isNearEndNow && !isLoadingMore && hasMore) {
        setIsNearEnd(true);
        loadMore();
      } else {
        setIsNearEnd(false);
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [isLoadingMore, hasMore, loadMore]);

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = container.clientWidth * 0.8; // Scroll 80% of container width
      const scrollPosition =
        direction === "left"
          ? container.scrollLeft - scrollAmount
          : container.scrollLeft + scrollAmount;

      container.scrollTo({
        left: scrollPosition,
        behavior: "smooth",
      });
    }
  };

  if (productsLoading && !productsList.length) {
    return (
      <div className="relative mx-auto w-full max-w-[1728px] px-2 py-2 md:px-12">
        <div className="relative px-4">
          <div className="flex gap-16 px-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="aspect-[4/5] w-[340px] animate-pulse rounded-[40px] bg-neutral-100"
              ></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (productsError || !productsList || productsList.length === 0) {
    return null;
  }

  return (
    <div className="relative mx-auto w-full px-2 pb-2 md:px-12 lg:max-w-[1240px] 2xl:max-w-[1728px]">
      <div className="relative px-4">
        {/* Scroll Container */}
        <div
          ref={scrollContainerRef}
          className="scrollbar-hide flex snap-x snap-mandatory gap-8 overflow-x-auto overflow-y-hidden px-4 py-4 md:gap-16"
        >
          {productsList.map((product) => (
            <div key={product._id} className="flex-none snap-start">
              <ProductCard product={product} />
            </div>
          ))}

          {/* Loading indicator */}
          {isLoadingMore && (
            <div className="flex aspect-[4/5] w-[340px] flex-none snap-start items-center justify-center">
              <div className="border-t-primary h-12 w-12 animate-spin rounded-full border-4 border-neutral-100"></div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <button
          onClick={() => scroll("left")}
          className="group absolute top-1/2 -left-10 hidden size-10 -translate-y-1/2 items-center justify-center rounded-full bg-[#FAFAFA] transition-colors hover:bg-neutral-50 hover:shadow-sm md:flex"
          aria-label="Previous products"
        >
          <ArrowLeft className="size-5 stroke-1 transition-all group-hover:stroke-2" />
        </button>

        <button
          onClick={() => scroll("right")}
          className="group absolute top-1/2 -right-10 hidden size-10 -translate-y-1/2 items-center justify-center rounded-full bg-[#FAFAFA] transition-colors hover:bg-neutral-50 hover:shadow-sm md:flex"
          aria-label="Next products"
        >
          <ArrowRight className="size-5 stroke-1 transition-all group-hover:stroke-2" />
        </button>
      </div>
    </div>
  );
}
