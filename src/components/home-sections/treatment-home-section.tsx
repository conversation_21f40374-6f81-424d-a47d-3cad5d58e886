"use client";
import React, { useRef } from "react";
import Image from "next/image";

const treatments = [
  { title: "Anti-aging", image: "/images/image1.png" },
  { title: "Weight Loss", image: "/images/image2.png" },
  { title: "Sleep", image: "/images/image3.png" },
  { title: "Hair loss", image: "/images/image4.png" },
  { title: "Injury repair", image: "/images/image6.jpeg" },
];

const TreatmentHomeSection = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = React.useState(0);
  console.log(currentIndex);

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = container.clientWidth; // Scroll by container width
      const scrollPosition =
        direction === "left"
          ? container.scrollLeft - scrollAmount
          : container.scrollLeft + scrollAmount;

      container.scrollTo({
        left: scrollPosition,
        behavior: "smooth",
      });
    }
  };

  const nextSlide = () => {
    scroll("right");
    setCurrentIndex((prev) => (prev === treatments.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    scroll("left");
    setCurrentIndex((prev) => (prev === 0 ? treatments.length - 1 : prev - 1));
  };

  return (
    <section className="overflow-hidden py-12 md:py-16">
      <div className="relative max-w-[1728px] px-4 lg:px-8">
        {/* Header */}
        <div className="mb-12 flex flex-col items-center">
          <h2 className="mb-4 text-center text-4xl font-medium text-black">
            Select Your Treatment
          </h2>
          <p className="max-w-[527px] text-center leading-normal text-neutral-600">
            Browse our range of expertly curated treatment options. <br />
            Click on the category that best aligns with your wellness goals
          </p>
        </div>

        {/* Navigation Buttons */}
        <div className="mb-8 hidden justify-end gap-4 lg:flex">
          <button
            onClick={prevSlide}
            className="flex size-16 items-center justify-center rounded-full border border-[#e9e9e9] bg-white transition-colors hover:bg-gray-50"
          >
            <svg width="25" height="19" viewBox="0 0 25 19" fill="none">
              <path
                d="M23.5 9.5H1.5M1.5 9.5L9.75 1.25M1.5 9.5L9.75 17.75"
                stroke="black"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          <button
            onClick={nextSlide}
            className="flex size-16 items-center justify-center rounded-full border border-[#e9e9e9] bg-white transition-colors hover:bg-gray-50"
          >
            <svg width="25" height="19" viewBox="0 0 25 19" fill="none">
              <path
                d="M1.5 9.5H23.5M23.5 9.5L15.25 1.25M23.5 9.5L15.25 17.75"
                stroke="black"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Treatments Scroller */}
        <div className="relative overflow-hidden">
          <div
            ref={scrollContainerRef}
            className="scrollbar-hide flex snap-x snap-mandatory gap-4 overflow-x-auto pb-6"
          >
            {treatments.map((treatment, index) => (
              <div key={index} className="w-[300px] flex-none snap-start md:w-[372px]">
                <div className="relative mb-4 aspect-square">
                  <Image
                    src={treatment.image}
                    alt={treatment.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="flex items-center justify-between rounded-[33.50px] bg-white/30 p-4 backdrop-blur-lg">
                  <h3 className="font-[syne] text-3xl font-normal text-black">{treatment.title}</h3>
                  <div className="flex size-9 items-center justify-center">
                    <svg
                      width="26"
                      height="20"
                      viewBox="0 0 26 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 10H25M25 10L16 1M25 10L16 19"
                        stroke="black"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TreatmentHomeSection;
