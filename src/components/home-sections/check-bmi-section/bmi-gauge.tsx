import React from "react";

interface BMIGaugeProps {
  bmi?: number;
  isMale?: boolean;
}

const BMIGauge = ({ bmi, isMale }: BMIGaugeProps) => {
  if (bmi === undefined) {
    return null;
  }

  const isUnderweight1 = (value: number) => value < 16;
  const isUnderweight2 = (value: number) => value >= 16 && value < 18.5;
  const isNormalWeight = (value: number) => value >= 18.5 && value < 25;
  const isOverweight = (value: number) => value >= 25 && value < 30;
  const isObese = (value: number) => value >= 30 && value < 40;
  const isSeverelyObese = (value: number) => value >= 40;

  // Color to use for filling the active section
  const fillColor = isMale ? "#4fa097" : "#ffa068";
  const strokeColor = isMale ? "#4fa097" : "#ffa068";

  return (
    <div className="relative h-full w-full transition-all duration-500">
      <svg
        width="478"
        height="248"
        viewBox="0 0 478 248"
        fill="none"
        className="h-full w-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_5032_4878)">
          <mask id="path-1-inside-1_5032_4878" fill="white">
            <path d="M106.977 58.5893C101.886 51.7041 103.345 41.945 110.573 37.3533C137.437 20.2866 167.476 8.74645 198.905 3.41825C207.29 1.99677 214.876 8.18478 215.767 16.6424V16.6424C216.66 25.1148 210.494 32.6464 202.109 34.1524C176.1 38.8236 151.226 48.3927 128.83 62.343C121.619 66.8343 112.028 65.4197 106.977 58.5893V58.5893Z" />
          </mask>
          <path
            d="M106.977 58.5893C101.886 51.7041 103.345 41.945 110.573 37.3533C137.437 20.2866 167.476 8.74645 198.905 3.41825C207.29 1.99677 214.876 8.18478 215.767 16.6424V16.6424C216.66 25.1148 210.494 32.6464 202.109 34.1524C176.1 38.8236 151.226 48.3927 128.83 62.343C121.619 66.8343 112.028 65.4197 106.977 58.5893V58.5893Z"
            fill={isNormalWeight(bmi) ? fillColor : "transparent"}
            stroke={isMale ? "#4fa097" : "#ffa068"}
            strokeWidth="4"
            mask="url(#path-1-inside-1_5032_4878)"
          />
          <mask id="path-2-inside-2_5032_4878" fill="white">
            <path d="M35.028 147.706C27.1701 144.261 23.5658 135.056 27.5533 127.459C40.632 102.541 58.0651 80.1314 79.0363 61.2799C85.3548 55.6002 95.0607 56.7515 100.36 63.3924V63.3924C105.702 70.0876 104.532 79.8107 98.2223 85.6034C81.1577 101.27 66.8593 119.676 55.9259 140.05C51.8939 147.564 42.8374 151.13 35.028 147.706V147.706Z" />
          </mask>
          <path
            d="M35.028 147.706C27.1701 144.261 23.5658 135.056 27.5533 127.459C40.632 102.541 58.0651 80.1314 79.0363 61.2799C85.3548 55.6002 95.0607 56.7515 100.36 63.3924V63.3924C105.702 70.0876 104.532 79.8107 98.2223 85.6034C81.1577 101.27 66.8593 119.676 55.9259 140.05C51.8939 147.564 42.8374 151.13 35.028 147.706V147.706Z"
            fill={isUnderweight2(bmi) ? fillColor : "transparent"}
            stroke={strokeColor}
            strokeWidth="4"
            mask="url(#path-2-inside-2_5032_4878)"
          />
          <path
            d="M224.223 15.9543C223.656 7.45705 230.093 0.0580501 238.609 0.0438366C291.178 -0.0439 342.324 17.1489 384.05 48.9313C390.866 54.1233 391.49 63.9788 385.827 70.4092V70.4092C380.211 76.7866 370.531 77.3798 363.726 72.2908C328.223 45.7425 285.064 31.2194 240.64 30.8721C232.133 30.8056 224.79 24.4423 224.223 15.9543V15.9543Z"
            fill={isOverweight(bmi) ? fillColor : "transparent"}
            stroke={strokeColor}
            strokeWidth="2"
          />
          <mask id="path-4-inside-3_5032_4878" fill="white">
            <path d="M461.274 219.479C469.815 218.801 476.237 211.311 474.991 202.835C468.007 155.344 446.678 111.063 413.838 75.8775C408.038 69.6641 398.264 69.9614 392.402 76.1153V76.1153C386.49 82.3208 386.806 92.1158 392.588 98.4427C419.873 128.301 437.757 165.472 444.013 205.329C445.34 213.781 452.745 220.157 461.274 219.479V219.479Z" />
          </mask>
          <path
            d="M461.274 219.479C469.815 218.801 476.237 211.311 474.991 202.835C468.007 155.344 446.678 111.063 413.838 75.8775C408.038 69.6641 398.264 69.9614 392.402 76.1153V76.1153C386.49 82.3208 386.806 92.1158 392.588 98.4427C419.873 128.301 437.757 165.472 444.013 205.329C445.34 213.781 452.745 220.157 461.274 219.479V219.479Z"
            fill={isObese(bmi) || isSeverelyObese(bmi) ? fillColor : "transparent"}
            stroke={strokeColor}
            strokeWidth="4"
            mask="url(#path-4-inside-3_5032_4878)"
          />
          <mask id="path-5-inside-4_5032_4878" fill="white">
            <path d="M16.2245 227.429C7.66777 227.057 0.982157 219.806 1.92127 211.292C3.7163 195.02 7.2018 178.976 12.3219 163.418C14.9884 155.315 24.026 151.485 31.9503 154.642V154.642C39.9204 157.817 43.7313 166.853 41.1341 175.03C37.2386 187.294 34.5055 199.894 32.9705 212.665C31.9492 221.163 24.7749 227.802 16.2245 227.429V227.429Z" />
          </mask>
          <path
            d="M16.2245 227.429C7.66777 227.057 0.982157 219.806 1.92127 211.292C3.7163 195.02 7.2018 178.976 12.3219 163.418C14.9884 155.315 24.026 151.485 31.9503 154.642V154.642C39.9204 157.817 43.7313 166.853 41.1341 175.03C37.2386 187.294 34.5055 199.894 32.9705 212.665C31.9492 221.163 24.7749 227.802 16.2245 227.429V227.429Z"
            fill={isUnderweight1(bmi) ? fillColor : "transparent"}
            stroke={strokeColor}
            strokeWidth="4"
            mask="url(#path-5-inside-4_5032_4878)"
          />
        </g>
        <text
          x="70"
          y="170"
          textAnchor="middle"
          className="font-quinn text-2xl font-light"
          fill={isMale ? "#4fa097" : "#B69A82"}
        >
          16
        </text>
        <text
          x="140"
          y="100"
          textAnchor="middle"
          className="font-quinn text-2xl font-light"
          fill={isMale ? "#4fa097" : "#B69A82"}
        >
          18.5
        </text>
        <text
          x="220"
          y="60"
          textAnchor="middle"
          className="font-quinn text-2xl font-light"
          fill={isMale ? "#4fa097" : "#B69A82"}
        >
          25
        </text>
        <text
          x="360"
          y="100"
          textAnchor="middle"
          className="font-quinn text-2xl font-light"
          fill={isMale ? "#4fa097" : "#B69A82"}
        >
          30
        </text>
        <text
          x="420"
          y="220"
          textAnchor="middle"
          className="font-quinn text-2xl font-light"
          fill={isMale ? "#4fa097" : "#B69A82"}
        >
          40
        </text>
        <defs>
          <clipPath id="clip0_5032_4878">
            <rect width="477" height="243" fill="white" transform="translate(0.5 0.0439453)" />
          </clipPath>
        </defs>
      </svg>
      <div className="absolute right-1/2 bottom-3 left-1/2 flex -translate-x-1/2 transform flex-col items-center gap-0 md:bottom-6">
        <p
          className={`text-2md font-light whitespace-nowrap md:text-lg ${isMale ? "text-[#4fa097]" : "text-[#B69A82]"}`}
        >
          Your BMI
        </p>
        <p className="text-5xl font-medium text-black">{bmi}</p>
      </div>
    </div>
  );
};

export default BMIGauge;
