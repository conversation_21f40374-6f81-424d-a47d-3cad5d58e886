"use client";

import Badge from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Switch from "@/components/ui/switch";
import { Minus, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import BMIGauge from "./bmi-gauge";

export function BmiCalculator({
  isMale,
  onIsMaleChange,
}: {
  isMale: boolean;
  onIsMaleChange: (isMale: boolean) => void;
}) {
  const [height, setHeight] = useState(170);
  const [weight, setWeight] = useState(68);
  const [bmi, setBmi] = useState(0);
  const [bmiCategory, setBmiCategory] = useState("");
  const [bmiDescription, setBmiDescription] = useState("");

  useEffect(() => {
    calculateBMI();
  }, []);

  const calculateBMI = () => {
    if (height <= 0 || weight <= 0) return;
    let calculatedBMI = 0;
    calculatedBMI = weight / ((height / 100) * (height / 100));

    setBmi(Number.parseFloat(calculatedBMI.toFixed(1)));

    // Set BMI category and description
    if (calculatedBMI < 18.5) {
      setBmiCategory("Underweight");
      setBmiDescription(
        "This BMI range suggests that you are underweight. Being in this category may increase your risk for certain health conditions."
      );
    } else if (calculatedBMI >= 18.5 && calculatedBMI < 25) {
      setBmiCategory("Normal weight");
      setBmiDescription(
        "This BMI range suggests that you are at a healthy weight for your height. Maintaining a healthy weight may reduce your risk of developing certain health conditions."
      );
    } else if (calculatedBMI >= 25 && calculatedBMI < 30) {
      setBmiCategory("Overweight");
      setBmiDescription(
        "This BMI range suggests that you are overweight. Being in this category may increase your risk for health complications, and it might be a good idea to discuss lifestyle changes with our healthcare provider."
      );
    } else if (calculatedBMI >= 30 && calculatedBMI < 35) {
      setBmiCategory("Obesity Class 1 (BMI 30 to 34.9)");
      setBmiDescription(
        "This BMI range suggests that you are in the early stage of obesity. Being in this category may increase your risk for health complications, and it might be a good idea to discuss lifestyle changes with our healthcare provider to improve your overall health."
      );
    } else if (calculatedBMI >= 35 && calculatedBMI < 40) {
      setBmiCategory("Obesity Class 2 (BMI 35 to 39.9)");
      setBmiDescription(
        "This BMI range suggests that you are in the moderate stage of obesity. Being in this category increases your risk for health complications."
      );
    } else {
      setBmiCategory("Obesity Class 3 (BMI 40 or higher)");
      setBmiDescription(
        "This BMI range suggests that you are in the severe stage of obesity. Being in this category significantly increases your risk for health complications."
      );
    }
  };

  const increaseHeight = () => setHeight((prev) => prev + 1);
  const decreaseHeight = () => setHeight((prev) => (prev > 1 ? prev - 1 : 1));
  const increaseWeight = () => setWeight((prev) => prev + 1);
  const decreaseWeight = () => setWeight((prev) => (prev > 1 ? prev - 1 : 1));

  const findBadgeColor = (
    value: number
  ): "error" | "warning" | "success" | "primary" | "secondary" | "pill" => {
    if (value <= 16) return "error";
    if (value > 16 && value < 18.5) return "warning";
    if (value >= 18.5 && value <= 25) return "success";
    if (value > 25 && value < 30) return "warning";
    return "error";
  };

  return (
    <div className="flex flex-col items-center gap-4 sm:gap-6 lg:h-[600px] lg:flex-row transition-all duration-500 ease-in-out">
      <div
        className={`inline-flex h-full w-full flex-col items-center justify-between gap-4 rounded-[40px] px-3 py-5 sm:gap-6 sm:px-5 sm:py-5 md:w-auto ${isMale ? "bg-[#f3f8f6]" : "bg-[#fffaf7]"} transition-all duration-300 ease-in-out`}
      >
        <div className="font-henju flex w-full items-center justify-center">
          <div className="inline-flex h-16 w-full items-center justify-between gap-2 self-stretch rounded-[32px] bg-white px-3 py-2 sm:h-20 sm:gap-4 sm:px-6 md:h-24 md:gap-6 md:px-12">
            <span
              className={`${isMale ? "text-black" : "text-gray-400"} text-md sm:text-2md md:text-lg`}
            >
              Male
            </span>
            <Switch
              checked={!isMale}
              onCheckedChange={() => onIsMaleChange(!isMale)}
              className={`${isMale ? "data-[state=checked]:bg-[#4fa097] data-[state=unchecked]:bg-[#4fa097]" : "data-[state=checked]:bg-[#ffa068] data-[state=unchecked]:bg-[#ffa068]"} `}
            />
            <span
              className={`${!isMale ? "text-black" : "text-gray-400"} text-md sm:text-2md md:text-lg`}
            >
              Female
            </span>
          </div>
        </div>
        <MetricInput
          title="Height"
          value={height}
          unit={"cm"}
          decrease={decreaseHeight}
          increase={increaseHeight}
        />
        <MetricInput
          title="Weight"
          value={weight}
          unit={"kg"}
          decrease={decreaseWeight}
          increase={increaseWeight}
        />
        <Button
          onClick={calculateBMI}
          variant={isMale ? "primary" : "gradient"}
          withArrow
        >
          Calculate your BMI
        </Button>
      </div>
      <div className="flex h-full w-full flex-col gap-4 px-2 sm:px-4">
        <div
          className={`xs:h-[200px] relative h-[180px] rounded-[40px] p-4 sm:h-[220px] sm:p-6 md:h-[250px] md:p-8 lg:h-1/2 ${isMale ? "bg-[#f3f8f6]" : "bg-[#fffaf7]"}`}
        >
          <BMIGauge bmi={bmi} isMale={isMale} />
        </div>

        <div
          className={`relative flex h-auto flex-col justify-between gap-3 rounded-[40px] px-4 py-4 sm:h-1/2 sm:gap-4 sm:px-6 sm:py-5 ${isMale ? "bg-[#f3f8f6]" : "bg-[#fffaf7]"}`}
        >
          {bmi > 0 && (
            <div className="flex flex-col gap-3 sm:gap-6">
              <div className="flex items-center">
                <Badge withDot variant={findBadgeColor(bmi)} size="lg">
                  {bmiCategory.split(" ")[0]}
                </Badge>
              </div>
              <div>
                <h3 className="text-md font-medium sm:text-2md">{bmiCategory}</h3>
                <p className="line-clamp-3 text-md font-light text-[#535353] sm:line-clamp-none sm:text-2md">
                  {bmiDescription}
                </p>
              </div>
            </div>
          )}
          <div className="mt-3 flex w-full items-center justify-center sm:mt-0">
            <Button
              variant={isMale ? "primary" : "gradient"}
              className=""
              withArrow
            >
              Book a Consultation
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

const MetricInput = ({
  unit,
  decrease,
  value,
  title,
  increase,
}: {
  title: string;
  unit: string;
  decrease: () => void;
  value: number;
  increase: () => void;
}) => {
  return (
    <div className="inline-flex h-36 w-full flex-col items-start justify-start gap-4 self-stretch rounded-[32px] bg-white px-3 py-4 sm:h-40 sm:gap-6 sm:px-4 sm:py-5 md:h-44 md:gap-9 md:px-5 md:py-7">
      <label className="font-henju mb-0 block text-2md sm:mb-1 sm:text-lg md:text-xl">
        {`${title} (${unit})`}
      </label>
      <div className="flex w-full items-center">
        <button
          onClick={decrease}
          className="touch-manipulation rounded-full bg-[#FFE2E2] p-1 transition-all duration-200 hover:scale-105 hover:bg-[#ffcaca] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#EDEDED]"
          aria-label={`Decrease ${title.toLowerCase()}`}
        >
          <Minus className="size-3 stroke-[#873A3A] stroke-1 disabled:stroke-[#999999] sm:size-4" />
        </button>
        <div className="flex-1 text-center text-lg sm:text-xl">
          {value} <span className="text-md sm:text-2md">{unit}</span>
        </div>
        <button
          onClick={increase}
          className="rounded-full bg-[#E6FAF7] p-1 transition-all duration-200 hover:scale-105 hover:bg-[#d0f5ef] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#E6FAF7]"
          aria-label={`Increase ${title.toLowerCase()}`}
        >
          <Plus className="size-3 stroke-[#40BFAA] stroke-1 disabled:stroke-[#999999] sm:size-4" />
        </button>
      </div>
    </div>
  );
};
