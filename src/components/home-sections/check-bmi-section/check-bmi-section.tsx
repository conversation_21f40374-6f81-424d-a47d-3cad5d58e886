"use client";
import Image from "next/image";
import { useIsMobile } from "@/hooks/use-media-query";
import { BmiCalculator } from "./bmi-calculator";
import { useState } from "react";

const CheckBMISection = () => {
  const isMobile = useIsMobile();
  const [isMale, setIsMale] = useState(false);

  return (
    <section className="flex flex-col items-center px-4 sm:px-8 md:px-12 lg:px-16">
      <div className="w-full max-w-[1728px]">
        <div className="mb-8 flex flex-col items-center md:mb-12">
          <div className="text-center">
            <span className="font-henju text-2xl font-normal text-black sm:text-3xl md:text-4xl lg:text-5xl">
              Check your{" "}
            </span>
            <span
              className={`font-henju text-2xl font-normal sm:text-3xl md:text-4xl lg:text-5xl ${isMale ? "text-[#4fa097]" : "text-[#ffa068]"} transition-all duration-500 ease-in-out`}
            >
              BMI
            </span>
            <span className="font-henju text-2xl font-normal text-black sm:text-3xl md:text-4xl lg:text-5xl">
              {" "}
              to Get Started
            </span>
          </div>
          <p className="text-md sm:text-2md mt-2 w-full text-center font-light text-[#535353] sm:max-w-[500px] md:mt-4 md:max-w-[700px]">
            BMI stands for Body Mass Index. It's a measurement that uses your height and weight to
            estimate if your weight is in a healthy range for your height.
          </p>
        </div>
        <div className="flex w-full flex-col gap-6 sm:gap-8 md:flex-row md:gap-12">
          <div className="flex w-full justify-center transition-all duration-500 ease-in-out md:w-1/3 md:justify-start">
            <Image
              alt="bmi calculator header image"
              width={400}
              height={600}
              className="h-auto w-full max-w-[300px] rounded-3xl object-contain transition-all duration-500 ease-in-out sm:max-w-[350px] md:h-[500px] md:max-w-[400px] lg:h-[600px]"
              src={isMale ? "/images/home/<USER>" : "/images/header/image4.jpg"}
            />
          </div>
          <div className="mt-6 w-full md:mt-0 md:w-2/3">
            <BmiCalculator isMale={isMale} onIsMaleChange={(value: boolean) => setIsMale(value)} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default CheckBMISection;
