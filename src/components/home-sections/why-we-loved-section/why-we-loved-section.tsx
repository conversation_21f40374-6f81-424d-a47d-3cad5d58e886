"use client";

import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
import Badge from "@/components/ui/badge";

const reviews = [
  {
    images: ["/images/why-we-love/review1.png", "/images/why-we-love/review5.png"],
    text: "I had severe hair loss and bald patches, but after this treatment, my hair started growing back stronger and fuller. Amazing results! Never imagined I’d see real growth again. This gave me back my confidence.",
    name: "<PERSON><PERSON>, 52",
    tag: "Hair Loss",
    stars: 5,
    tagStyle: "border border-[#69A792] text-[#69A792]",
  },
  {
    images: ["/images/why-we-love/review2.png", "/images/why-we-love/review8.png"],
    text: "My skin was full of breakouts and acne scars. After the treatment, it's much cleaner, smoother, and I feel more confident. Even the dark spots faded with time. It really transformed my skin.",
    name: "<PERSON>, 52",
    tag: "Skincare",
    stars: 5,
    tagStyle: "border border-[#FAC2A7] text-[#FFA068]",
  },
  {
    images: ["/images/why-we-love/review4.png", "/images/why-we-love/review6.png"],
    text: "My skin was always red and sensitive. Even simple products made it worse. After using this treatment, the irritation is gone, and my skin feels calm and smooth.",
    name: "Taylor, 28",
    tag: "Skincare",
    stars: 5,
    tagStyle: "border border-[#FAC2A7] text-[#FFA068]",
  },
  {
    images: ["/images/why-we-love/review3.png", "/images/why-we-love/review7.png"],
    text: "My face looked dull and with uneven tone. I tried many things with no luck. This treatment brightened my skin and gave a healthy, even glow. Highly recommend!",
    name: "Kelly, 30",
    tag: "Skincare",
    stars: 5,
    tagStyle: "border border-[#FAC2A7] text-[#FFA068]",
  },
  {
    images: ["/images/why-we-love/review1.png", "/images/why-we-love/review5.png"],
    text: "I had severe hair loss and bald patches, but after this treatment, my hair started growing back stronger and fuller. Amazing results! Never imagined I’d see real growth again. This gave me back my confidence.",
    name: "Makel, 52",
    tag: "Hair Loss",
    stars: 5,
    tagStyle: "border border-[#69A792] text-[#69A792]",
  },
  {
    images: ["/images/why-we-love/review2.png", "/images/why-we-love/review8.png"],
    text: "My skin was full of breakouts and acne scars. After the treatment, it's much cleaner, smoother, and I feel more confident. Even the dark spots faded with time. It really transformed my skin.",
    name: "Linda, 52",
    tag: "Skincare",
    stars: 5,
    tagStyle: "border border-[#FAC2A7] text-[#FFA068]",
  },
  {
    images: ["/images/why-we-love/review4.png", "/images/why-we-love/review6.png"],
    text: "My skin was always red and sensitive. Even simple products made it worse. After using this treatment, the irritation is gone, and my skin feels calm and smooth.",
    name: "Taylor, 28",
    tag: "Skincare",
    stars: 5,
    tagStyle: "border border-[#FAC2A7] text-[#FFA068]",
  },
  {
    images: ["/images/why-we-love/review3.png", "/images/why-we-love/review7.png"],
    text: "My face looked dull and with uneven tone. I tried many things with no luck. This treatment brightened my skin and gave a healthy, even glow. Highly recommend!",
    name: "Kelly, 30",
    tag: "Skincare",
    stars: 5,
    tagStyle: "border border-[#FAC2A7] text-[#FFA068]",
  },
];

const WhyWeLoved = () => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [pageCount, setPageCount] = useState(0);

  const handleScroll = () => {
    if (scrollRef.current) {
      const container = scrollRef.current;
      const scrollLeft = container.scrollLeft;
      const visibleWidth = container.offsetWidth;

      const index = Math.ceil(scrollLeft / visibleWidth);
      setActiveIndex(index);
    }
  };

  useEffect(() => {
    const container = scrollRef.current;
    if (!container) return;

    const updatePageCount = () => {
      const visibleWidth = container.offsetWidth;
      const scrollWidth = container.scrollWidth;

      const count = Math.ceil(scrollWidth / visibleWidth);
      setPageCount(count);
    };

    updatePageCount();
    window.addEventListener("resize", updatePageCount);

    container.addEventListener("scroll", handleScroll, { passive: true });
    return () => {
      container.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", updatePageCount);
    };
  }, []);

  return (
    <section className="bg-[#0C7885] py-16">
      <div className="mb-11 text-center">
        <h2 className="font-henju text-2xl font-normal text-white sm:text-3xl md:text-4xl lg:text-5xl">
          Why we love <span className="text-[#85B5A5]">Revolved</span>
        </h2>
        <p className="font-quinn text-2md sm:text-2md mt-2 font-thin text-white">
          Real stories from real patients—see how personalized treatment made a difference in their
          lives.
        </p>
      </div>

      <div
        ref={scrollRef}
        className="scrollbar-hide flex snap-x snap-mandatory gap-6 overflow-x-auto scroll-smooth px-4"
      >
        {reviews.map((item, index) => (
          <div
            key={index}
            className="w-[280px] shrink-0 snap-start rounded-3xl bg-[#0D5159] p-4 text-white sm:w-[340px]"
          >
            <div className="font-quinn text-2md mb-4 flex justify-center gap-2 font-thin">
              {item.images.map((src, i) => (
                <Image
                  key={i}
                  src={src}
                  alt={`Before and After ${index + 1}`}
                  width={50}
                  height={50}
                  className="w-[30%] rounded-md object-cover"
                />
              ))}
            </div>
            <div className="text-2md mb-4 flex h-[100px] items-center justify-center text-center font-thin text-white">
              "{item.text}"
            </div>
            <div className="text-2md mb-4 flex items-center justify-center gap-2 font-thin">
              {Array.from({ length: item.stars }).map((_, starIndex) => (
                <span key={starIndex} className="text-xl text-[#FEBE00]">
                  ★
                </span>
              ))}
            </div>

            <div className="flex items-center justify-center gap-2">
              <div className="text-2md font-semibold text-white">{item.name}</div>

              <Badge
                size="sm"
                className={cn(
                  "w-[76px] justify-center outline-none",
                  item.tag === "Hair Loss"
                    ? "border border-[#BFE2D6] bg-[#0D5159] text-[#3C9C7C]"
                    : "border border-[#FAC2A7] bg-[#0D5159] text-[#FFA068]"
                )}
              >
                {item.tag}
              </Badge>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 flex justify-center gap-2">
        {Array.from({ length: pageCount }).map((_, i) => (
          <span
            key={i}
            className={`h-[10px] rounded-full transition-colors duration-300 ${
              activeIndex === i ? "w-[37px] bg-white" : "w-[20px] bg-white/40"
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default WhyWeLoved;
