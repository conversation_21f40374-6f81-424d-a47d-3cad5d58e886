import { MessageSquareIcon } from "lucide-react";
import { motion } from "framer-motion";

interface ChatBotButtonProps {
  onClick: () => void;
  isMobile: boolean;
}

const ChatBotButton = ({ onClick, isMobile }: ChatBotButtonProps) => {
  return (
    <motion.button
      className={`fixed ${isMobile ? "right-4 bottom-4 size-14" : "right-10 bottom-10 size-16"} z-[10] flex cursor-pointer items-center justify-center rounded-[120px] bg-white p-3 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] hover:shadow-[0px_4px_30px_0px_rgba(0,0,0,0.08)]`}
      onClick={onClick}
      aria-label="Chat with us"
      initial={{ scale: 0, opacity: 0 }}
      animate={{
        scale: 1,
        opacity: 1,
        transition: {
          type: "spring",
          damping: 25,
          stiffness: 300,
          duration: 0.3,
        },
      }}
      exit={{
        scale: 0,
        opacity: 0,
        transition: {
          duration: 0.2,
        },
      }}
      whileHover={{ scale: 1.05 }}
      style={{ transformOrigin: "bottom right" }}
    >
      <MessageSquareIcon
        className={`${isMobile ? "size-7" : "size-9"} fill-[#0c7885] stroke-[#0c7885]`}
      />
    </motion.button>
  );
};

export default ChatBotButton;
