import { X, Send, Mic } from "lucide-react";
import React, { useState, useRef, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import { v4 as uuidv4 } from "uuid";
import MicIcon from "@/components/icons/mic-icon";
import SendIcon from "@/components/icons/send-icon";

interface ChatCardProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile: boolean;
}

interface Message {
  id: string;
  text: string;
  type: "user" | "bot";
  timestamp: Date;
}

// Bot response options
const botResponses = [
  "Hello! How can I help you today?",
  "I'm here to assist with any questions about our products.",
  "Thanks for reaching out! What would you like to know about Revolved?",
  "I'd be happy to help you find the right treatment for your needs.",
  "Is there anything specific you'd like to know about our skincare products?",
  "I can provide information about our treatments and products. What are you interested in?",
];

// Get a random bot response
const getRandomBotResponse = (): string => {
  const randomIndex = Math.floor(Math.random() * botResponses.length);
  return botResponses[randomIndex];
};

// User Message Component
const UserMessage = ({ text }: { text: string }) => (
  <div
    className={`inline-flex min-h-[48px] max-w-[90%] items-center justify-start gap-2.5 rounded-tl-[25px] rounded-br-[25px] rounded-bl-[25px] bg-[#f3f8f6] md:min-h-[54px] md:max-w-[80%]`}
  >
    <div className={`px-3 text-md font-normal text-[#0c7885] md:px-4 md:text-2md`}>{text}</div>
  </div>
);

// Bot Message Component
const BotMessage = ({ text }: { text: string }) => (
  <div className={`flex max-w-[90%] items-end gap-2 md:max-w-[80%]`}>
    <div className="rounded-full bg-white p-1">
      <Image src={"/icons/logo.svg"} alt="Logo" width={16} height={16} />
    </div>
    <div
      className={`inline-flex min-h-[48px] max-w-[250px] items-center justify-center gap-2.5 rounded-tl-[25px] rounded-tr-[25px] rounded-br-[25px] bg-neutral-50 p-3 md:min-h-[54px] md:max-w-[294px] md:p-4`}
    >
      <div className={`justify-start text-md font-normal text-[#4f4f4f] md:text-2md`}>{text}</div>
    </div>
  </div>
);

const ChatCard = ({ isOpen, onClose, isMobile }: ChatCardProps) => {
  // State for messages
  const [messages, setMessages] = useState<Message[]>([
    {
      id: uuidv4(),
      text: "Hello Revolved, how are you today?",
      type: "user",
      timestamp: new Date(),
    },
    {
      id: uuidv4(),
      text: "Hello, I'm fine, how can I help you?",
      type: "bot",
      timestamp: new Date(),
    },
  ]);

  // State for input text
  const [inputText, setInputText] = useState("");

  // State to track if bot is "typing"
  const [isBotTyping, setIsBotTyping] = useState(false);

  // Ref for messages container to auto-scroll
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Function to scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Function to send a message
  const sendMessage = (text: string) => {
    if (!text.trim()) return;

    // Add user message
    const newUserMessage: Message = {
      id: uuidv4(),
      text: text.trim(),
      type: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newUserMessage]);
    setInputText(""); // Clear input

    // Set bot typing state
    setIsBotTyping(true);

    // Simulate bot response after a delay
    setTimeout(() => {
      const botResponse: Message = {
        id: uuidv4(),
        text: getRandomBotResponse(),
        type: "bot",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, botResponse]);
      setIsBotTyping(false);
    }, 1500); // 1.5 second delay for bot response
  };

  // Handle input submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(inputText);
  };

  // Animation variants based on mobile status
  const variants = {
    initial: {
      scale: 0,
      opacity: 0,
      transformOrigin: isMobile ? "bottom center" : "bottom right",
    },
    animate: {
      scale: 1,
      opacity: 1,
      transformOrigin: isMobile ? "bottom center" : "bottom right",
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 300,
        duration: 0.3,
      },
    },
    exit: {
      scale: 0,
      opacity: 0,
      transformOrigin: isMobile ? "bottom center" : "bottom right",
      transition: {
        duration: 0.2,
      },
    },
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className={`fixed z-[10] flex flex-col rounded-[20px] bg-white shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] md:rounded-[30px] ${
            isMobile
              ? "inset-x-4 bottom-4 gap-3 p-4"
              : "right-10 bottom-10 w-full gap-6 p-6 md:w-1/3 max-w-[600px] md:gap-6"
          }`}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={variants}
        >
          <div className="flex w-full justify-between">
            <div className="justify-start">
              <span
                className={`font-henju font-normal text-black ${isMobile ? "text-xl" : "text-2xl sm:text-3xl"}`}
              >
                Let's{" "}
              </span>
              <span
                className={`font-henju font-normal text-[#0c7885] ${isMobile ? "text-xl" : "text-2xl sm:text-3xl"}`}
              >
                Chat!
              </span>
            </div>
            <button onClick={onClose} className="touch-manipulation">
              <X
                className={`${isMobile ? "size-4" : "size-5"} stroke-1 hover:stroke-2`}
                color="#0c7885"
              />
            </button>
          </div>

          {/* Messages container with scrolling */}
          <div className="md:h-max-[500px] flex h-[300px] max-h-[400px] flex-col gap-4 overflow-y-auto p-1 md:h-[440px] xl:h-[500px]">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`w-full ${message.type === "user" ? "justify-end" : "justify-start"} flex`}
              >
                {message.type === "user" ? (
                  <UserMessage text={message.text} />
                ) : (
                  <BotMessage text={message.text} />
                )}
              </div>
            ))}

            {/* Bot typing indicator */}
            {isBotTyping && (
              <div className="relative">
                <div className="absolute top-[31px] left-0">
                  <Image src={"/icons/logo.svg"} alt="Logo" width={20} height={20} />
                </div>
                <div
                  className={`ml-[33px] inline-flex items-center justify-center gap-2.5 rounded-tl-[25px] rounded-tr-[25px] rounded-br-[25px] bg-neutral-50 ${
                    isMobile ? "min-h-[48px] max-w-[250px]" : "min-h-[54px] max-w-[294px]"
                  }`}
                >
                  <div
                    className={`flex justify-start font-normal text-[#4f4f4f] ${
                      isMobile ? "px-3 text-[13px]" : "px-4 text-[15px]"
                    }`}
                  >
                    <span className="flex gap-1">
                      <span className="animate-bounce">.</span>
                      <span className="animate-bounce" style={{ animationDelay: "0.2s" }}>
                        .
                      </span>
                      <span className="animate-bounce" style={{ animationDelay: "0.4s" }}>
                        .
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Invisible element to scroll to */}
            <div ref={messagesEndRef} />
          </div>

          {/* Chat input form */}
          <form onSubmit={handleSubmit}>
            <ChatInput
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onSend={handleSubmit}
              disabled={isBotTyping}
            />
          </form>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ChatCard;

interface ChatInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSend: (e: React.FormEvent) => void;
  disabled?: boolean;
}

const ChatInput = ({ value, onChange, onSend, disabled }: ChatInputProps) => {
  return (
    <div className="relative inline-flex h-14 w-full items-center justify-start gap-2.5 rounded-full bg-white pl-[22px] shadow-[5px_4px_20px_0px_rgba(0,0,0,0.13)]">
      <input
        className="w-[calc(100%_-_70px)] justify-start text-[13px] font-semibold text-black outline-none placeholder:text-[#a1a1a1] focus:outline-none"
        placeholder="Write your message"
        value={value}
        onChange={onChange}
        disabled={disabled}
      />
      <button
        type="submit"
        className="absolute right-4 cursor-pointer touch-manipulation"
        disabled={disabled || !value.trim()}
        onClick={onSend}
      >
        <SendIcon className="size-5 stroke-[#0C7885] stroke-2" color="#0C7885" />
      </button>
      <div className="absolute right-11 cursor-pointer touch-manipulation">
        <MicIcon className="stroke-1.5 size-5 stroke-[#CECECE]" color="#CECECE" />
      </div>
    </div>
  );
};
