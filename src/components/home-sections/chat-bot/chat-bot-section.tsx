"use client";
import React from "react";
import ChatBotButton from "./chat-bot-button";
import ChatCard from "./chat-card";
import { AnimatePresence } from "framer-motion";
import { useIsMobile } from "@/hooks/use-media-query";

const ChatBotSection = () => {
  const isMobile = useIsMobile();
  const [isChatCardOpen, setIsChatCardOpen] = React.useState(false);

  const handleChatCardToggle = () => {
    setIsChatCardOpen((prev) => !prev);
  };

  const handleChatCardClose = () => {
    setIsChatCardOpen(false);
  };

  return (
    <>
      <AnimatePresence>
        {!isChatCardOpen && <ChatBotButton onClick={handleChatCardToggle} isMobile={isMobile} />}
      </AnimatePresence>
      <ChatCard isOpen={isChatCardOpen} onClose={handleChatCardClose} isMobile={isMobile} />
    </>
  );
};

export default ChatBotSection;
