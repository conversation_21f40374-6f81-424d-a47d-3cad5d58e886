import React from "react";
import Image from "next/image";

const CustomMadeForYouHomeSection = () => {
  return (
    <section className="relative w-full overflow-hidden">
      {/* Background SVG */}

      <div data-svg-wrapper className="absolute inset-x-0 top-[10%] w-full md:top-[15%]">
        <svg
          width="1728"
          height="490"
          viewBox="0 0 1728 490"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0 382.986V0.486328L340.5 38.4863L928 0.486328L1224.5 38.4863L1731 0.486328V489.986L0 382.986Z"
            fill="#F4FEFF"
          />
        </svg>
      </div>

      {/* Content Container */}
      <div className="container mx-auto px-4 lg:px-8">
        <div className="relative grid min-h-[500px] grid-cols-1 items-center gap-8 lg:min-h-[600px] lg:grid-cols-2">
          {/* Text Content */}
          <div className="z-10 space-y-6 py-12 lg:py-0">
            <h2 className="font-[syne] text-3xl font-normal text-[#23a1b0] md:text-4xl lg:text-6xl">
              Custom-Made For You
            </h2>
            <p className="max-w-[527px] text-2md leading-normal font-normal text-[#23a1b0]">
              Discover skincare that&apos;s uniquely yours. Enjoy products crafted specifically for
              your skin, ensuring a perfect fit every time.
            </p>
            <button className="bg-[#23a1b0] px-6 py-3 text-2md font-medium text-white transition-colors hover:bg-[#1c8a97]">
              Create your Formula Now
            </button>
          </div>

          {/* Image */}
          <div className="relative mx-auto aspect-[510/579] w-full max-w-[510px] lg:absolute lg:top-[-20px] lg:right-[-100px] lg:mx-0">
            <Image
              src="/images/image5.png"
              alt="Custom made skincare"
              fill
              className="rotate-[1.0deg] transform object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default CustomMadeForYouHomeSection;
