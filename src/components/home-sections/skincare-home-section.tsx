import React from "react";
import Image from "next/image";

const SkincareHomeSection = () => {
  return (
    <section className="relative h-[600px] w-full overflow-hidden py-8 md:py-16 lg:h-[800px] lg:py-20">
      {/* Background Wave */}
      <div className="absolute top-64 left-0 w-full scale-x-105 transform">
        <svg
          className="h-auto w-full"
          viewBox="0 0 1728 610"
          fill="none"
          preserveAspectRatio="xMidYMid slice"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M1731 503V0L1390.5 38L803 0L506.5 38L0 0V610L1731 503Z" fill="#23A1B0" />
        </svg>
      </div>
      <div className="relative container mx-auto px-4 lg:px-8">
        <div className="grid grid-cols-2 items-center gap-8 lg:gap-24">
          {/* Image Container */}
          <div className="relative order-2 lg:order-1">
            <div className="absolute -top-10 -left-12 mx-auto aspect-[719/745] w-full max-w-[719px]">
              <Image
                src="/images/skincare-img.png"
                alt="Skincare"
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>

          {/* Content Container */}
          <div className="relative top-80 z-10 order-1 lg:order-2">
            <h2 className="mb-6 max-w-[455px] text-3xl font-normal text-white md:text-4xl lg:text-6xl">
              Tailored Skincare
            </h2>
            <p className="mb-8 max-w-[527px] text-2md leading-normal font-normal text-white">
              Discover skincare that&apos;s uniquely yours. Enjoy products crafted specifically for
              your skin, ensuring a perfect fit every time.
            </p>
            <button className="hover:bg-opacity-90 inline-flex items-center justify-center bg-white px-6 py-4 text-2md font-medium text-[#23a1b0] transition-colors">
              Create your Formula Now
            </button>
          </div>
        </div>

        {/* Decorative Stars */}
        <div className="absolute top-96 right-1/2 hidden lg:block">
          <svg
            width="58"
            height="70"
            viewBox="0 0 58 70"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="h-[70px] w-[58px]"
          >
            <path
              d="M57.0331 35.3836C36.0633 38.832 32.3353 43.2123 29.5394 68.7489C29.4462 69.5877 28.2346 69.5877 28.1414 68.7489C25.3454 43.2123 21.6174 38.9252 0.647648 35.3836C-0.191143 35.2904 -0.191143 34.0788 0.647648 33.9856C21.6174 30.5373 25.3454 26.2501 28.1414 0.713566C28.2346 -0.125225 29.4462 -0.125225 29.5394 0.713566C32.3353 26.2501 36.0633 30.4441 57.0331 33.9856C57.7787 34.0788 57.7787 35.1972 57.0331 35.3836Z"
              fill="white"
            />
          </svg>
        </div>
      </div>
    </section>
  );
};

export default SkincareHomeSection;
