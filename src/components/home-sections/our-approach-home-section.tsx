import React from "react";
import ApproachCard from "@/components/approach-card";

const approachCards = [
  {
    title: "Initial Health Screening",
    description: "Understand your unique health profile with a thorough screening.",
    icon: "/icons/approach1.svg",
  },
  {
    title: "Personalized Plan",
    description: "Get a tailored treatment plan designed specifically for you.",
    icon: "/icons/approach2.svg",
  },
  {
    title: "Expert Consultation",
    description: "Connect with our specialists for professional guidance.",
    icon: "/icons/approach3.svg",
  },
  {
    title: "Ongoing Support",
    description: "Receive continuous care and monitoring throughout your journey.",
    icon: "/icons/approach3.svg",
  },
];

const OurApproachHomeSection = () => {
  return (
    <section className="py-12 md:py-16 lg:py-24">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mx-auto mb-12 max-w-2xl text-center">
          <h2 className="mb-4 text-3xl font-normal text-black md:text-4xl">Our Approach</h2>
          <p className="text-2md leading-normal font-light text-neutral-600">
            At Revolved Clinic, your journey to optimal health begins with a comprehensive,
            science-led process
          </p>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 justify-items-center gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {approachCards.map((card, index) => (
            <ApproachCard
              key={index}
              title={card.title}
              description={card.description}
              icon={card.icon}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurApproachHomeSection;
