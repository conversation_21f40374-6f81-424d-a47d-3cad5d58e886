"use client";

import { useEffect, useRef, useMemo } from "react";
import { useGetInfiniteProducts } from "@/api/product-service";
import { ProductCard } from "@/components/product-card";
import ProductItemSkeloton from "../skeleton/product-item-skeleton";

interface InfiniteProductListProps {
  category?: string;
  limit?: number;
  searchQuery?: string;
}

export const InfiniteProductList = ({
  category = "",
  limit = 10,
  searchQuery = "",
}: InfiniteProductListProps) => {
  // console.log("InfiniteProductList category", category);
  const {
    productsList,
    productsLoading,
    productsError,
    productsEmpty,
    isLoadingMore,
    hasMore,
    loadMore,
    totalItems,
    revalidateProductsList,
  } = useGetInfiniteProducts({
    category: category === "all" ? "" : category,
    limit,
    searchQuery,
  });

  // Reference to the sentinel element for infinite scrolling
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Revalidate products when category or search query changes
  useEffect(() => {
    revalidateProductsList();
  }, [category, searchQuery, revalidateProductsList]);

  // Set up the intersection observer for infinite scrolling
  useEffect(() => {
    if (!hasMore || isLoadingMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create a new observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // If the sentinel element is visible and we have more items to load
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          loadMore();
        }
      },
      { threshold: 0.1 } // Trigger when 10% of the element is visible
    );

    // Observe the sentinel element
    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, isLoadingMore, loadMore]);

  // Memoize the product grid to prevent unnecessary re-renders
  const productGrid = useMemo(() => {
    if (productsLoading && productsList.length === 0) {
      // Show skeleton loaders while loading
      return (
        <div className="grid w-full max-w-7xl grid-cols-1 place-items-center gap-8 sm:grid-cols-2 lg:grid-cols-3 2xl:max-w-[1728px] 2xl:grid-cols-4">
          {Array.from({ length: limit }).map((_, index) => (
            <ProductItemSkeloton key={index} />
          ))}
        </div>
      );
    }

    if (productsError) {
      return (
        <div className="flex w-full justify-center py-12 text-red-500">
          Error loading products: {productsError.message}
        </div>
      );
    }

    if (productsEmpty) {
      return (
        <div className="flex w-full flex-col items-center py-12 text-neutral-500">
          <div className="mb-2 text-xl font-medium">No products found</div>
          <div className="text-2md">Try adjusting your filters or search query</div>
        </div>
      );
    }

    return (
      <div className="grid w-full max-w-7xl grid-cols-2 place-items-center gap-4 sm:grid-cols-2 md:gap-12 lg:grid-cols-3 2xl:max-w-[1728px] 2xl:grid-cols-4">
        {productsList.map((product) => (
          <ProductCard key={product._id} product={product} />
        ))}
      </div>
    );
  }, [productsList, productsLoading, productsError, productsEmpty, limit]);

  return (
    <div className="flex w-full flex-col items-center">
      {/* Product count display */}
      {/* {!productsLoading && !productsError && !productsEmpty && (
        <div className="w-full max-w-7xl 2xl:max-w-[1728px] mb-4 text-neutral-500 text-2md">
          Showing {productsList.length} of {totalItems} products
        </div>
      )} */}

      {/* Product grid with memoization */}
      {productGrid}

      {/* Loading indicator and sentinel element for infinite scrolling */}
      <div ref={loadMoreRef} className="flex w-full justify-center py-8">
        {isLoadingMore && (
          <div className="flex items-center gap-2">
            <div className="border-t-primary h-5 w-5 animate-spin rounded-full border-2 border-neutral-300"></div>
            <div className="text-neutral-500">Loading more products...</div>
          </div>
        )}
        {/* {!hasMore && productsList.length > 0 && (
          <div className="text-neutral-500 text-2md">No more products to load</div>
        )} */}
      </div>
    </div>
  );
};
