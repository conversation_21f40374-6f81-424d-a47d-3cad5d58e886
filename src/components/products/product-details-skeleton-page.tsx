import React from "react";

const ProductDetailsSkeletonPage = () => {
  return (
    <div className="mt-16 flex min-h-screen w-full flex-col items-center gap-4 bg-white px-4 sm:px-6 md:mt-28 md:gap-16 lg:px-16">
      {/* Product Detail Skeleton */}
      <div className="flex w-full flex-col gap-4 md:flex-row md:gap-8 lg:gap-20">
        {/* Product Preview Container Skeleton */}
        <div className="flex aspect-square w-full max-w-xl flex-col rounded-3xl border border-neutral-50 bg-neutral-50 px-4 pt-8 pb-4 md:aspect-[500/450] md:rounded-[45px]">
          {/* Main Product Image Skeleton */}
          <div className="relative h-3/4 w-full">
            <div className="relative h-full w-full p-4 md:p-8">
              <div className="h-full w-full animate-pulse rounded-[30px] bg-neutral-200" />
            </div>

            {/* Navigation Arrows Skeleton */}
            <div className="absolute top-1/2 left-0 size-10 -translate-y-1/2 animate-pulse rounded-full bg-white md:size-12" />
            <div className="absolute top-1/2 right-0 size-10 -translate-y-1/2 animate-pulse rounded-full bg-white md:size-12" />

            {/* Image Indicator Dots Skeleton */}
            <div className="absolute bottom-6 left-1/2 flex -translate-x-1/2 gap-2 rounded-2xl bg-white px-4 py-1">
              {[1, 2, 3].map((_, index) => (
                <div key={index} className="size-1.5 animate-pulse rounded-full bg-neutral-200" />
              ))}
            </div>
          </div>

          {/* Thumbnail Images Skeleton */}
          <div className="flex h-1/4 w-full items-end justify-center gap-12 px-4">
            {[1, 2].map((_, index) => (
              <div
                key={index}
                className="relative size-12 animate-pulse overflow-hidden rounded-xl bg-neutral-200 md:size-16"
              />
            ))}
          </div>
        </div>

        {/* Product Details Skeleton */}
        <div className="max-w-xl flex-1 py-2 pr-2 pl-2 md:pr-16">
          <div className="flex flex-col gap-4">
            {/* Title Skeleton */}
            <div className="h-10 w-3/4 animate-pulse rounded-full bg-neutral-200" />

            {/* Description Skeleton */}
            <div className="h-6 w-full animate-pulse rounded-full bg-neutral-200" />
            <div className="h-6 w-2/3 animate-pulse rounded-full bg-neutral-200" />

            {/* Price Skeleton */}
            <div className="mt-2 h-8 w-24 animate-pulse rounded-full bg-neutral-200" />
          </div>

          {/* Accordion Items Skeleton */}
          <div className="mx-auto mt-8 w-full">
            {[1, 2, 3].map((_, index) => (
              <div key={index} className="border-b border-[#f4feff] py-4">
                <div className="mb-2 h-6 w-1/2 animate-pulse rounded-full bg-neutral-200" />
                {index === 0 && (
                  <div className="mt-2 pl-4">
                    <div className="mb-2 h-4 w-full animate-pulse rounded-full bg-neutral-200" />
                    <div className="h-4 w-3/4 animate-pulse rounded-full bg-neutral-200" />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Add to Bag Section Skeleton */}
          <div className="mt-8 flex gap-4 pr-2">
            <div className="h-12 w-full min-w-30 animate-pulse rounded-full bg-neutral-200 md:w-48" />
            <div className="h-12 w-80 min-w-36 animate-pulse rounded-full bg-neutral-200" />
          </div>
        </div>
      </div>

      {/* Clinical Results Skeleton */}
      <div className="relative flex w-full flex-col gap-8 overflow-hidden rounded-[30px] border border-neutral-50 bg-[#fffaf7] p-4 md:gap-12 md:rounded-[50px] md:px-8 md:py-12 lg:flex-row lg:gap-12">
        <div className="flex w-full flex-col gap-4 lg:w-1/2">
          <div className="gap-3">
            <div className="mb-2 h-10 w-1/2 animate-pulse rounded-full bg-neutral-200" />
          </div>
          <div className="h-6 w-3/4 animate-pulse rounded-full bg-neutral-200" />
          <div className="flex flex-col gap-4">
            {[1, 2, 3].map((_, index) => (
              <div
                key={index}
                className="h-[80px] w-full max-w-[616px] animate-pulse rounded-full bg-white"
              />
            ))}
          </div>
        </div>
        <div className="flex w-full flex-col gap-4 sm:flex-row md:gap-6 lg:w-1/2">
          {[1, 2].map((_, index) => (
            <div
              key={index}
              className="relative h-[280px] w-full animate-pulse rounded-[30px] bg-neutral-200 sm:h-[320px] sm:w-1/2 md:h-[400px] md:rounded-[50px] lg:h-[420px]"
            />
          ))}
        </div>
      </div>

      {/* Suggested Products Skeleton */}
      <div className="relative mx-auto w-full max-w-[1728px] px-2 py-12 md:px-12">
        <div className="mb-8 h-10 w-1/3 animate-pulse rounded-full bg-neutral-200" />

        <div className="relative px-4">
          <div className="scrollbar-hide flex snap-x snap-mandatory gap-16 overflow-x-auto px-4">
            {[1, 2, 3, 4].map((_, index) => (
              <div key={index} className="flex-none snap-start">
                <div className="relative aspect-[3/4] w-[340px] rounded-[40px] bg-neutral-50 p-3">
                  <div className="flex h-full w-full flex-col items-center justify-center p-3">
                    {/* Image container */}
                    <div className="relative w-full flex-[0_0_70%]">
                      <div className="h-full w-full animate-pulse rounded-[30px] bg-neutral-200" />
                    </div>

                    {/* Content container */}
                    <div className="flex w-full flex-[0_0_30%] flex-col items-center justify-end gap-1 pb-3">
                      <div className="h-7 w-3/4 animate-pulse rounded-full bg-neutral-200" />
                      <div className="h-4 w-3/4 animate-pulse rounded-full bg-neutral-200" />
                      <div className="mt-1 h-6 w-1/2 animate-pulse rounded-full bg-neutral-200" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Buttons Skeleton */}
          <div className="absolute top-1/2 -left-10 hidden size-10 -translate-y-1/2 animate-pulse rounded-full bg-neutral-200 md:block" />
          <div className="absolute top-1/2 -right-10 hidden size-10 -translate-y-1/2 animate-pulse rounded-full bg-neutral-200 md:block" />
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsSkeletonPage;
