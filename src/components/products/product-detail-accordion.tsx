"use client";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface ProductDetailAccordionItemProps {
  title: string;
  content: string;
  className?: string;
  initiallyOpen?: boolean;
}

const ProductDetailAccordionItem = ({
  title,
  content,
  className,
  initiallyOpen = false,
}: ProductDetailAccordionItemProps) => {
  const [isOpen, setIsOpen] = useState(initiallyOpen);

  return (
    <div
      className={cn(
        `w-full rounded-3xl border-b border-[#e9e9e9] bg-neutral-50 px-5 py-6`,
        className
      )}
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`inline-flex w-full items-center justify-between gap-4`}
      >
        <span className="text-2xl leading-normal font-medium text-black">{title}</span>
        <div className="relative flex size-5 items-center justify-center">
          <svg
            width="14"
            height="13"
            viewBox="0 0 14 13"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={cn("transition-transform duration-300", isOpen && "rotate-45")}
          >
            <path
              d="M7.00002 0.666992V12.3337M1.16669 6.50033H12.8334"
              stroke="#F2A472"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </button>

      {/* Content */}
      <div
        className={cn(
          "grid bg-neutral-50 transition-all duration-300",
          isOpen ? "grid-rows-[1fr]" : "grid-rows-[0fr]"
        )}
      >
        <div className="overflow-hidden">
          <div className="text-2md px-2 py-4 text-gray-600">{content}</div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailAccordionItem;
