import { IProductSingleItem } from "@/types/product";
import { ArrowLeft, ArrowRight } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ProductImage {
  id: number;
  src: string;
  alt: string;
}

const ProductPreviewContainer = ({ product }: { product: IProductSingleItem }) => {
  if (!product) {
    return null;
  }

  const productImages: ProductImage[] = [
    {
      id: 1,
      src: product?.main_image || "/images/product-detail-placeholder.svg",
      alt: "Product main view",
    },
    ...product.images.map((image, idx) => ({
      id: idx + 2,
      src: image,
      alt: "Product view" + idx + 2,
    })),
  ];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handlePrevImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? productImages.length - 1 : prev - 1));
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prev) => (prev === productImages.length - 1 ? 0 : prev + 1));
  };

  const handleThumbnailClick = (index: number) => {
    setCurrentImageIndex(index);
  };
  return (
    <div className="flex aspect-square w-full max-w-5xl flex-col rounded-3xl 2xl:aspect-video 2xl:max-w-full">
      {/* Main Product Image */}
      <div className="relative h-full w-full bg-neutral-50 px-4 pt-8 pb-4 md:rounded-[45px]">
        <div className="relative h-full w-full p-4 md:p-8">
          <Image
            src={productImages[currentImageIndex].src}
            alt={productImages[currentImageIndex].alt}
            fill
            className="object-contain p-2"
            priority
          />
        </div>

        {/* Navigation Arrows */}
        {/* <button
          onClick={handlePrevImage}
          className="absolute top-1/2 left-0 flex size-10 -translate-y-1/2 items-center justify-center rounded-full bg-white transition-transform hover:scale-105 hover:shadow-lg md:size-12"
          aria-label="Previous image"
        >
          <ArrowLeft className="size-5 stroke-1 text-black md:size-6" />
        </button> */}

        {/* <button
          onClick={handleNextImage}
          className="absolute top-1/2 right-0 flex size-10 -translate-y-1/2 items-center justify-center rounded-full bg-white transition-transform hover:scale-105 hover:shadow-lg md:size-12"
          aria-label="Next image"
        >
          <ArrowRight className="size-5 stroke-1 text-black md:size-6" />
        </button> */}

        {/* Image Indicator Dots */}
        {/* <div className="absolute bottom-6 left-1/2 flex -translate-x-1/2 gap-2 rounded-2xl bg-white px-4 py-1">
          {productImages.map((_, index) => (
            <div
              key={index}
              className={`size-1.5 rounded-full transition-colors duration-200 ${
                index === currentImageIndex ? "bg-[#ffa068]" : "bg-[#fac2a7]"
              }`}
            />
          ))}
        </div> */}
      </div>

      <div className="flex w-full items-end justify-center gap-4 px-4 pt-12 pb-4">
        {productImages.map((image, index) => (
          <button
            key={image.id}
            onClick={() => handleThumbnailClick(index)}
            className={`relative size-12 overflow-hidden rounded-xl transition-transform hover:scale-105 md:size-16 ${
              currentImageIndex === index ? "ring-1 ring-[#ffa068]" : ""
            }`}
          >
            <Image src={image.src} alt={image.alt} fill className="object-cover" />
          </button>
        ))}
      </div>
    </div>
  );
};

export default ProductPreviewContainer;
