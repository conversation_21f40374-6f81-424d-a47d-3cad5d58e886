"use client";

import AccordionItem from "@/components/accordion-item";
import BagIcon from "@/components/icons/bag-icon";
import ProductPreviewContainer from "@/components/products/product-preview-container";
import { Button } from "@/components/ui/button";
import { useBagStore } from "@/store/bag-store";
import { IProductSingleItem } from "@/types/product";
import { Minus, Plus } from "lucide-react";
import { useState } from "react";
import ProductDetailAccordionItem from "./product-detail-accordion";
import TickCheckIcon from "../icons/tick-check-icon";
import Image from "next/image";

interface ProductDetailProps {
  product: IProductSingleItem;
  isLoading: boolean;
  error: any;
  isEmpty: boolean;
}

const frequencies = [
  { _id: 1, title: "Subscribe", desc: "Get refills automatically" },
  { _id: 2, title: "One - time purchase", desc: "$75" },
];

export const ProductDetail = ({ product, isLoading, error, isEmpty }: ProductDetailProps) => {
  const [quantity, setQuantity] = useState(1);

  const [selectedFrequencyId, setSelectedFrequencyId] = useState(0);

  const handleSelectFrequency = (frequencyId: number) => {
    setSelectedFrequencyId(frequencyId);
  };

  const addItemToBag = useBagStore((state) => state.addItem);

  const handleAddItemToBag = () => {
    if (!product) return;

    addItemToBag({
      productId: product,
      quantity,
      isPrescription: false,
    });
  };

  if (isLoading) {
    return (
      <div className="flex w-full justify-center py-12">
        <div className="animate-pulse">Loading product details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex w-full justify-center py-12 text-red-500">
        Error loading product: {error.message}
      </div>
    );
  }

  if (isEmpty || !product) {
    return (
      <div className="flex w-full justify-center py-12 text-neutral-500">Product not found.</div>
    );
  }

  return (
    <div className="flex w-full flex-col gap-4 md:flex-row md:gap-8 lg:gap-4">
      <div className="flex w-full flex-col gap-4 lg:w-2/3">
        <ProductPreviewContainer product={product} />
        <div className="mx-auto mt-4 flex w-full flex-col gap-4">
          {product.productDetails &&
            product.productDetails.map((item, index) => (
              <ProductDetailAccordionItem
                key={index}
                title={item.title}
                content={item.description}
                className="border-b border-[#f4feff]"
              />
            ))}
        </div>
      </div>
      <div className="w-full max-w-xl flex-1 py-2 pr-4 pl-2 md:pr-16 lg:w-1/3">
        <div className="flex flex-col gap-1">
          <h2 className="text-2xl font-light text-[#f2a472]">
            {product?.categories[0]?.name || "Unknown Category"}
          </h2>
          <h1 className="text-3xl font-medium text-black">{product?.name || "Unknown Product"}</h1>
          <p className="text-md justify-start leading-normal font-light text-[#535353]">
            {product?.description || "No description available for this product."}
          </p>
          <p className="font-dm-sans mt-1 w-20 justify-start text-2xl leading-normal font-medium text-black">
            ${product.price}
          </p>
        </div>

        <div className="mt-8 flex flex-col gap-4">
          <h2 className="text-2xl font-light text-black">{"Frequency"}</h2>
          <div className="flex flex-row gap-2">
            {frequencies.map((frequency) => (
              <div
                key={frequency._id}
                onClick={() => handleSelectFrequency(frequency._id)}
                className={`group text-2md relative min-h-[100px] w-full max-w-[325px] min-w-[100px] cursor-pointer rounded-xl border px-4 py-[8px] ${
                  selectedFrequencyId === frequency._id
                    ? "border-[#f2a472] bg-[#fff8f4]"
                    : "border-gray-200 bg-white"
                }`}
              >
                <div className="flex h-full items-start justify-between gap-2">
                  <div
                    className={`text-md flex h-full flex-col justify-end font-medium ${
                      selectedFrequencyId === frequency._id ? "text-black" : "text-[#535353]"
                    }`}
                  >
                    {frequency.title}
                    <p className="text-sm font-light">{frequency.desc}</p>
                  </div>

                  {selectedFrequencyId === frequency._id && (
                    <span className="text-[#f2a472]">
                      <TickCheckIcon color="#f2a472" />
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-8 flex flex-col gap-4">
          <div>
            <h2 className="text-2xl font-light text-black">{"Select Your Plan"}</h2>
            <p className="text-md leading-1 font-light">Pause or cancel whenever you like.</p>
          </div>
        </div>

        <div className="mt-8 flex w-full max-w-xl gap-4 pr-2 pb-8">
          <div className="flex h-12 w-fit min-w-30 flex-1 items-center justify-center rounded-full bg-white px-4 outline-1 outline-[#ededed] md:w-48">
            {quantity}
          </div>
          <button
            className="flex size-12 items-center justify-center rounded-full bg-[#EDEDED] p-1 transition-all duration-200 hover:scale-105 hover:bg-[#e0e0e0] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#EDEDED]"
            onClick={() => setQuantity((prev) => Math.max(1, prev - 1))}
            disabled={quantity <= 1}
          >
            <Minus className="size-5 stroke-[#535353] stroke-1 disabled:stroke-[#999999]" />
          </button>
          <button
            className="flex size-12 items-center justify-center rounded-full bg-[#fff8f4] p-1 transition-all duration-200 hover:scale-105 hover:bg-[#f2a472]/40 active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#E6FAF7]"
            onClick={() => setQuantity((prev) => Math.min(99, prev + 1))}
            disabled={quantity >= 99}
          >
            <Plus className="size-5 stroke-[#f2a472] stroke-1 disabled:stroke-[#999999]" />
          </button>
        </div>
        <Button
          variant="gradient"
          className="text-md md:text-2md h-12 w-full min-w-36 p-2 font-normal md:py-2 md:pr-2 md:pl-5"
          withArrow
          icon={<BagIcon />}
          onClick={handleAddItemToBag}
        >
          Add to Bag
        </Button>

        <div className="mt-8 flex w-full max-w-2xl flex-col gap-4 rounded-2xl bg-white p-6 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)]">
          <div className="flex gap-4">
            <Image
              src={"/icons/header/blood.svg"}
              alt={`skin care icon`}
              width={40}
              height={40}
              className="h-[32px] w-[32px] md:h-[52px] md:w-[52px]"
            />
            <div>
              <h2 className="text-xl font-medium text-black">Skincare</h2>
              <p className="text-md font-light text-[#ababab]">
                Still not sure? See if a prescription plan is right for you.
              </p>
            </div>
          </div>
          <Button
            withArrow
            variant="outline"
            className="mt-4 border-[#4fa097] text-[#4fa097] outline-[#4fa097]"
            iconColor="#4fa097"
          >
            Get Started
          </Button>
        </div>
      </div>
    </div>
  );
};
