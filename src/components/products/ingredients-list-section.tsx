import React from "react";
import Image from "next/image";

const IngredientsListSection = () => {
  const contents = [
    {
      title: "Retinol (Vitamin A)",
      desc: "Boosts skin cell turnover, reduces fine lines and wrinkles, and improves skin texture.",
      image: "/images/ingredients/image1.png",
    },

    {
      title: "Hyaluronic Acid (HA)",
      desc: "A powerful moisturizer that retains hydration in the skin, giving it a plump and youthful appearance while reducing wrinkles.",
      image: "/images/ingredients/image2.png",
    },
    {
      title: "Niacinamide (Vitamin B3)",
      desc: "Improves skin texture, minimizes dullness and fine lines, and helps even out skin tone.",
      image: "/images/ingredients/image3.png",
    },
    {
      title: "Niacinamide (Vitamin B3)",
      desc: "Improves skin texture, minimizes dullness and fine lines, and helps even out skin tone.",
      image: "/images/ingredients/image4.png",
    },
    {
      title: "Peptides",
      desc: "Short chains of amino acids that support collagen production, enhance skin elasticity, and reduce signs of aging.",
      image: "/images/ingredients/image5.png",
    },
    {
      title: "Coenzyme Q10 (CoQ10)",
      desc: "A potent antioxidant that helps fight aging at the cellular level and boosts energy production in skin cells.",
      image: "/images/ingredients/image6.png",
    },
  ];

  return (
    <div className="mt-4 flex w-full flex-col items-start gap-4 bg-white px-4 sm:px-6 md:gap-16">
      <h1 className="font-henju text-3xl font-normal text-black">Key Active Ingredients</h1>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-8 lg:grid-cols-3">
        {contents.map((item, index) => (
          <div
            key={index}
            className="flex flex-col items-center gap-4 rounded-[20px] bg-neutral-50 p-4"
          >
            <Image
              src={item.image}
              alt={item.title}
              width={300}
              height={300}
              className="h-[10rem] max-h-[10rem] w-[12.5rem] max-w-[12.5rem] object-cover"
            />
            <div className="flex flex-col items-center gap-2">
              <h2 className="font-henju text-2xl font-normal text-black">{item.title}</h2>
              <p className="text-md text-center font-light text-[#535353]">{item.desc}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default IngredientsListSection;
