"use client";

import { TreatmentItem } from "@/app/(protected)/(with-nav-footer)/my-treatments/[id]/page";
import { cn } from "@/lib/utils";
import React from "react";
import AccordionItem from "../accordion-item";
import Dialog from "../ui/dialog";
import ProductDetailImage from "./product-details-image";

interface ProductDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  treatment: TreatmentItem | null;
  className?: string;
}

const ProductDetailsDialog: React.FC<ProductDetailsDialogProps> = ({
  isOpen,
  onClose,
  treatment,
  className,
}) => {
  if (!treatment) return null;

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title={treatment.name || "Product Details"}
      className={cn(
        "h-[90vh] w-full max-w-[95vw] overflow-auto rounded-2xl md:h-[808px] md:w-[80vw]",
        className
      )}
    >
      <div className="relative mt-4 flex h-full w-full flex-col overflow-hidden rounded-2xl px-3 md:mt-8 md:flex-row md:px-6">
        {/* Left side content */}
        <div className="mb-6 w-full md:mb-0 md:w-1/2">
          <div className="flex flex-col items-center">
            <div className="relative mx-auto w-full max-w-[300px]">
              {/* Product image */}
              <ProductDetailImage
                imageUrl={treatment.imageUrl}
                alt={treatment.name || "Product"}
                compounds={treatment.compounds}
              />
            </div>

            {/* Info boxes */}
            {treatment.moreDetails?.map((item, index) => (
              <div
                className="mt-4 inline-flex w-full items-center justify-start rounded-2xl bg-[#f3fdff] px-4 py-2.5"
                key={index}
              >
                <div className="w-full justify-start">
                  <p className="text-2md font-normal text-[#23a1b0]">{item.title}</p>
                  <p className="text-md font-light text-neutral-600">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right side accordion sections */}
        <div className="w-full md:w-1/2">
          {treatment.details?.map((item, index) => (
            <div className="w-full" key={index}>
              <AccordionItem
                title={item.title}
                content={item.description}
                className="border-none text-2md"
                initiallyOpen={index === 0}
              />
            </div>
          ))}
        </div>
      </div>
    </Dialog>
  );
};

export default ProductDetailsDialog;
