"use client";

import React, { useEffect, useState } from "react";
import DropdownMenu from "../ui/dropdown-menu";
import { Calendar, CircleX, EllipsisVertical } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "../ui/button";
import ToastIcon from "../icons/toast-icon";
import RescheduleDialog from "./reschedule-dialog";
import { useToast } from "@/context/toast-context";
import { cancelBooking } from "@/api/booking-service";
import { BookingItem } from "@/types/booking";

interface TreatmentActionMenuProps {
  id: string;
  className?: string;
  revalidateBookings: () => void; // Optional prop for revalidation
  booking: BookingItem;
}

const TreatmentActionMenu: React.FC<TreatmentActionMenuProps> = ({
  id,
  className,
  revalidateBookings,
  booking,
}) => {
  const [dropdownPosition, setDropdownPosition] = useState<"right" | "bottom">("bottom");
  const [dropdownAlign, setDropdownAlign] = useState<"start" | "center" | "end">("center");
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [showRescheduleDialog, setShowRescheduleDialog] = useState(false);
  const { showToast } = useToast();

  useEffect(() => {
    const updatePosition = () => {
      setDropdownPosition(window.innerWidth >= 1024 ? "right" : "bottom");
      setDropdownAlign(window.innerWidth >= 1024 ? "center" : "end");
    };

    updatePosition();
    window.addEventListener("resize", updatePosition);
    return () => window.removeEventListener("resize", updatePosition);
  }, []);

  // Sample data for the current booking
  const currentBooking = {
    date: "Tue, 4 Mar, 2025",
    time: "01:30 pm",
    doctorName: "Dr. Fiona McKenzie",
    doctorSpecialty: "MBBS, is a specialist in anti-aging medicine",
  };

  const onReschedule = () => {
    setShowRescheduleDialog(true);
  };

  const onCancel = () => setShowCancelConfirm(true);
  const handleConfirmCancel = async () => {
    try {
      // Logic to cancel the treatment
      console.log(`Cancel treatment ${id}`);
      const response = await cancelBooking(id);
      if (response) {
        revalidateBookings();
        showToast("Booking cancelled successfully.", "success");
      }
    } catch (error: any) {
      console.error("Error cancelling treatment:", error);
      showToast(
        error?.response?.data?.message || "Failed to cancel the booking. Please try again later.",
        "error"
      );
      return;
    } finally {
      setShowCancelConfirm(false);
    }
  };
  const handleCancelNo = () => setShowCancelConfirm(false);
  const isBookingCancelled = booking.status === "cancelled";

  return (
    <>
      <DropdownMenu
        trigger={<EllipsisVertical className="size-4 text-[#535353]" />}
        position={dropdownPosition}
        align={dropdownAlign}
        distance={36}
        width="w-auto"
        className={cn("w-56 min-w-[180px]", className)}
      >
        {!showCancelConfirm ? (
          <div className="inline-flex size-auto flex-col items-start justify-start gap-2.5 rounded-2xl bg-white px-4 py-6">
            <button
              onClick={onReschedule}
              disabled={isBookingCancelled}
              className="text-2md flex items-center gap-2 leading-normal font-normal text-black transition-colors hover:text-[#23a1b0] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
            >
              <div className="relative">
                <Calendar className="size-4" />
              </div>
              Reschedule Booking
            </button>
            {
              <button
                onClick={onCancel}
                disabled={isBookingCancelled}
                className="text-2md flex items-center gap-2 leading-normal font-normal text-black transition-colors hover:text-[#23a1b0] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
              >
                <div className="relative">
                  <CircleX className="size-4" />
                </div>
                Cancel Booking
              </button>
            }
          </div>
        ) : (
          <div className="flex w-full flex-col gap-4 rounded-2xl bg-white p-4">
            <div className="flex w-full items-center gap-4">
              <div className="flex-shrink-0">
                <ToastIcon size="xs" />
              </div>
              <p className="text-md flex-1 leading-normal font-normal text-black">
                Are you sure you want to cancel this booking?
              </p>
            </div>
            <div className="flex w-full flex-col gap-3 sm:flex-row">
              <Button
                onClick={handleCancelNo}
                className="flex-1 items-center justify-center bg-[#e9e9e9] px-4 py-2 transition-colors duration-200 hover:bg-[#dbdbdb]"
              >
                <span className="text-md text-center font-medium text-black">No</span>
              </Button>
              <Button
                variant="primary"
                withArrow
                onClick={handleConfirmCancel}
                className="flex-1 items-center justify-center py-2 pr-2 pl-4 transition-colors duration-200"
              >
                <span className="text-md text-center font-medium text-white">Yes</span>
              </Button>
            </div>
          </div>
        )}
      </DropdownMenu>

      {/* Reschedule Dialog */}
      <RescheduleDialog
        isOpen={showRescheduleDialog}
        onClose={() => setShowRescheduleDialog(false)}
        treatmentId={id}
        currentBooking={currentBooking}
      />
    </>
  );
};

export default TreatmentActionMenu;
