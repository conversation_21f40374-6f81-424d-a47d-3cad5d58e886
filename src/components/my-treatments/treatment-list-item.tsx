import Image from "next/image";
import Link from "next/link";
import { Calendar, CheckIcon, Clock } from "lucide-react";
import TreatmentActionMenu from "./treatment-action-menu";
import Badge from "../ui/badge";
import { motion } from "framer-motion";
import { BookingItem } from "@/types/booking";
import { formatBookingDateTime, getBookingStatusColor } from "@/lib/utils";

interface TreatmentListItemProps {
  index: number;
  booking: BookingItem;
  revalidateBookings: () => void; // Optional prop for revalidation
}

const TreatmentListItem = ({ booking, index, revalidateBookings }: TreatmentListItemProps) => {
  const { dateString, timeString } = formatBookingDateTime(booking.bookingTime);

  return (
    <motion.div
      key={booking._id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="w-full max-w-4xl rounded-2xl bg-white shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]"
    >
      {/* Card Header */}
      <div className="flex w-full flex-wrap items-center justify-between gap-4 border-b border-[#e9e9e9] p-6">
        <div className="flex w-1/2 flex-col">
          <Link
            href={`/my-treatments/${booking._id}`}
            className="font-henju hover:text-primary text-lg transition-all duration-300 md:text-2xl"
          >
            {booking?.category?.name || "Unknown Treatment"}
          </Link>
          <p className="text-md text-neutral-600">Online Consultation</p>
        </div>
        <div className="flex items-center gap-1">
          <Badge
            variant={getBookingStatusColor(booking.status)}
            withDot
            size="sm"
            className="capitalize"
          >
            {booking.status}
          </Badge>
          <TreatmentActionMenu
            id={booking._id}
            revalidateBookings={revalidateBookings}
            booking={booking}
          />
        </div>
      </div>

      {/* Card Body */}
      <div className="flex flex-wrap items-center gap-6 border-b border-[#e9e9e9] p-6">
        <div className="flex flex-1 items-start gap-4">
          {booking?.doctor?.image ? (
            <Image
              src={booking?.doctor?.image}
              alt="Doctor"
              width={40}
              height={40}
              className="h-10 w-10 rounded-full object-cover"
            />
          ) : (
            <div className="flex size-10 items-center justify-center rounded-full bg-[#e9e9e9]">
              <Image
                src={"/icons/doctor-avatar-placeholder.svg"}
                alt="Doctor"
                width={30}
                height={30}
                className="rounded-full"
              />
            </div>
          )}
          <div>
            <p className="text-2md font-light text-black capitalize">
              {booking?.doctor?.name || "Dr. Unknown"}
            </p>
            <p className="text-md w-80 font-light text-neutral-600 capitalize">
              {booking?.doctorProfile?.education || "Unknown"}
            </p>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center gap-1">
            <div className="flex size-4 items-center justify-center">
              <Calendar className="size-3 text-[#535353]" />
            </div>
            <span className="text-md font-light text-black">{dateString}</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="flex size-4 items-center justify-center">
              <Clock className="size-3 text-[#535353]" />
            </div>
            <span className="text-md font-light text-black">{timeString}</span>
          </div>
        </div>
      </div>
      {booking.status !== "cancelled" && booking.postQuestionnaireCompletion && (
        <div className="relative flex w-full px-4 py-2">
          <div className="w-full rounded-tl-2xl rounded-tr-2xl bg-white p-1">
            <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <div className="relative size-3.5">
                    {booking.postQuestionnaireCompletion.isCompleted ? (
                      <div className="flex size-3.5 items-center justify-center rounded-full bg-[#23a1b0]">
                        <CheckIcon className="size-3 stroke-[#e9e9e9]" />
                      </div>
                    ) : (
                      <>
                        <div className="absolute size-3.5 rounded-full border border-[#e9e9e9]" />
                        <div
                          className="absolute size-3.5 rounded-full border border-[#23a1b0]"
                          style={{
                            clipPath: `polygon(0 0, ${booking.postQuestionnaireCompletion.percentage || 0}% 0, 100% 100%, 0 100%)`,
                          }}
                        />
                      </>
                    )}
                  </div>
                </div>
                <p className="text-md text-neutral-600">
                  {!booking.postQuestionnaireCompletion.isCompleted
                    ? "Please complete the following assessment before your appointment."
                    : "Thank you for completing the assessment. We will get back to you soon."}
                </p>
              </div>

              {!booking.postQuestionnaireCompletion.isCompleted && (
                <button className="flex items-center gap-2 rounded-full px-4 py-1 outline outline-offset-[-1px] outline-[#23a1b0] transition-colors hover:bg-[#23a1b0]/5">
                  <p className="text-[10px] whitespace-nowrap text-[#23a1b0]">Complete here</p>
                  <div className="flex h-[4px] w-[10px] items-center justify-center">
                    <svg
                      width="20"
                      height="16"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="flex-shrink-0"
                    >
                      <path
                        d="M3.33203 10.5H16.6654M16.6654 10.5L11.6654 5.5M16.6654 10.5L11.6654 15.5"
                        stroke="#23A1B0"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default TreatmentListItem;
