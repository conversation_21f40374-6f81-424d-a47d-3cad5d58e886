"use client";

import React from "react";
import Dialog from "../ui/dialog";
import DateSlotSelector from "../date-slot-selector";
import { Calendar, Clock } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface RescheduleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  treatmentId: string;
  currentBooking: {
    date: string;
    time: string;
    doctorName: string;
    doctorSpecialty: string;
  };
  className?: string;
}

const RescheduleDialog: React.FC<RescheduleDialogProps> = ({
  isOpen,
  onClose,
  treatmentId,
  currentBooking,
  className,
}) => {
  const handleReschedule = (date: string, time: string) => {
    console.log(`Rescheduling treatment ${treatmentId} to ${date} at ${time}`);
    // Here you would make an API call to reschedule the booking
    onClose();
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title="Reschedule Booking"
      className={cn("max-h-[95vh] w-full max-w-[95vw] md:max-h-[90vh] md:max-w-[720px]", className)}
    >
      <div className="flex h-full max-h-[calc(95vh-120px)] flex-col overflow-hidden md:max-h-[calc(90vh-120px)]">
        {/* Current Booking Info */}
        <div className="mb-3 border-b border-[#e9e9e9] px-2 pb-3 sm:mb-4 sm:px-4 sm:pb-4">
          <div className="mb-2 text-md font-normal text-neutral-600 sm:mb-3 sm:text-2md">
            Current Booking
          </div>
          <div className="flex items-start gap-2 sm:gap-3 md:gap-4">
            <div className="flex size-7 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-gray-100 sm:size-8 md:size-10">
              <Image
                src="/icons/doctor-avatar-placeholder.svg"
                alt={currentBooking.doctorName}
                className="size-6 object-cover sm:size-7 md:size-8"
                width={40}
                height={40}
                loading="lazy"
              />
            </div>
            <div className="min-w-0 flex-1">
              <div className="truncate text-md font-normal text-black sm:text-2md">
                {currentBooking.doctorName}
              </div>
              <div className="truncate text-[10px] font-normal text-neutral-600 sm:text-md md:text-2md">
                {currentBooking.doctorSpecialty}
              </div>
            </div>
          </div>
          <div className="mt-2 flex flex-wrap items-center gap-3 sm:mt-3 sm:gap-4 md:gap-8">
            <div className="flex items-center gap-1.5 sm:gap-2">
              <Calendar className="size-3 text-[#535353] sm:size-4" />
              <span className="text-md font-light text-black sm:text-2md">
                {currentBooking.date}
              </span>
            </div>
            <div className="flex items-center gap-1.5 sm:gap-2">
              <Clock className="size-3 text-[#535353] sm:size-4" />
              <span className="text-md font-light text-black sm:text-2md">
                {currentBooking.time}
              </span>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mb-2 px-2 sm:mb-3 sm:px-4 md:mb-4">
          <p className="text-md font-normal text-black sm:text-2md">
            Pick a new date and time for your consultation
          </p>
        </div>

        {/* Date Slot Selector - Wrapped in a scrollable container */}
        <div className="custom-scrollbar flex-1 overflow-y-auto px-2 pb-4 sm:px-4">
          <DateSlotSelector buttonText="Reschedule" onSubmit={handleReschedule} className="pb-4" />
        </div>
      </div>
    </Dialog>
  );
};

export default RescheduleDialog;
