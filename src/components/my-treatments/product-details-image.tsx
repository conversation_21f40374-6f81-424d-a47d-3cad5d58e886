import React, { useState, useEffect } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface ProductDetailImageProps {
  imageUrl: string;
  className?: string;
  imageClassName?: string;
  alt: string;
  compounds: string[];
}

// Define arrow directions
type ArrowDirection = "top-down" | "bottom-up" | "left-right" | "right-left";

// Define positions for the pointers
type PointerPosition =
  | "top-left"
  | "top-right"
  | "bottom-left"
  | "bottom-right"
  | "center-left"
  | "center-right";

// Define the compound detail with position and direction
interface CompoundDetail {
  text: string;
  position: PointerPosition;
  direction: ArrowDirection;
  tooltipPosition: "top" | "bottom" | "left" | "right";
}

const ProductDetailImage = ({
  imageUrl,
  className,
  alt,
  compounds,
  imageClassName,
}: ProductDetailImageProps) => {
  const [isMobile, setIsMobile] = useState(false);
  const [activeCompound, setActiveCompound] = useState<number | null>(null);

  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Cleanup
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  // Map compounds to positions with specific directions and tooltip positions
  const compoundDetails: CompoundDetail[] = compounds
    ? compounds.map((compound, index) => {
        // Define configurations for each position
        const configurations = [
          // First compound (top-right with downward arrow)
          {
            position: "top-right" as PointerPosition,
            direction: "top-down" as ArrowDirection,
            tooltipPosition: "top" as const,
          },
          // Second compound (bottom-left with upward arrow)
          {
            position: "bottom-left" as PointerPosition,
            direction: "bottom-up" as ArrowDirection,
            tooltipPosition: "bottom" as const,
          },
          // Additional positions if needed
          {
            position: "center-right" as PointerPosition,
            direction: "right-left" as ArrowDirection,
            tooltipPosition: "right" as const,
          },
          {
            position: "center-left" as PointerPosition,
            direction: "left-right" as ArrowDirection,
            tooltipPosition: "left" as const,
          },
          {
            position: "top-left" as PointerPosition,
            direction: "top-down" as ArrowDirection,
            tooltipPosition: "top" as const,
          },
          {
            position: "bottom-right" as PointerPosition,
            direction: "bottom-up" as ArrowDirection,
            tooltipPosition: "bottom" as const,
          },
        ];

        // Get configuration based on index
        const config = configurations[index % configurations.length];

        return {
          text: compound,
          position: config.position,
          direction: config.direction,
          tooltipPosition: config.tooltipPosition,
        };
      })
    : [];

  return (
    <div className={cn("relative", className)}>
      <div className="group relative">
        {/* Product image with subtle hover effect */}
        <div className="overflow-hidden rounded-lg">
          <Image
            src={imageUrl || "/images/treatments/product-detail-image.png"}
            alt={alt}
            width={261}
            height={260}
            className={cn(
              "h-60 w-full object-cover transition-transform duration-500 group-hover:scale-[102%] md:h-72",
              imageClassName
            )}
          />
        </div>

        {/* Render compound details with arrows */}
        {compoundDetails.map((detail, index) => (
          <ArrowWithDetails
            key={index}
            text={detail.text}
            position={detail.position}
            direction={detail.direction}
            tooltipPosition={detail.tooltipPosition}
            isActive={activeCompound === index}
          />
        ))}
      </div>
    </div>
  );
};

export default ProductDetailImage;

interface ArrowWithDetailsProps {
  text: string;
  position: PointerPosition;
  direction: ArrowDirection;
  tooltipPosition: "top" | "bottom" | "left" | "right";
  isActive?: boolean;
}

const ArrowWithDetails = ({
  text,
  position,
  direction,
  tooltipPosition,
  isActive = false,
}: ArrowWithDetailsProps) => {
  return (
    <div
      data-svg-wrapper
      className={cn(
        "absolute",
        // Position the wrapper based on position prop
        position === "top-left" && "top-4 left-4",
        position === "top-right" && "top-4 right-4",
        position === "bottom-left" && "bottom-4 left-4",
        position === "bottom-right" && "right-4 bottom-4",
        position === "center-left" && "top-1/2 left-4 -translate-y-1/2",
        position === "center-right" && "top-1/2 right-4 -translate-y-1/2"
      )}
    >
      <svg
        width="41"
        height="116"
        viewBox="0 0 41 116"
        fill="none"
        className={cn(
          // Rotate SVG based on direction
          direction === "bottom-up" && "rotate-180",
          direction === "left-right" && "rotate-90",
          direction === "right-left" && "-rotate-90"
        )}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M37.5 115.5V116H38V115.5H37.5ZM37.5 0.113249L34.6132 3L37.5 5.88675L40.3868 3L37.5 0.113249ZM0 115.5V116H37.5V115.5V115H0V115.5ZM37.5 115.5H38V3H37.5H37V115.5H37.5Z"
          fill="url(#paint0_linear_2509_8600)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_2509_8600"
            x1="18.75"
            y1="3"
            x2="18.75"
            y2="115.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#23A1B0" />
            <stop offset="1" stopColor="#23A1B0" stopOpacity="0.2" />
          </linearGradient>
        </defs>
      </svg>
      <div
        className={cn(
          "inline-flex items-center justify-start rounded-2xl bg-[#f3fdff] px-6 py-1 outline-1 outline-offset-[-1px] outline-[#23a1b0]/20",
          // Position the bubble based on tooltipPosition
          tooltipPosition === "top" &&
            "absolute -top-2 left-1/2 -translate-x-1/2 -translate-y-full transform",
          tooltipPosition === "bottom" &&
            "absolute -bottom-2 left-1/2 -translate-x-1/2 translate-y-full transform",
          tooltipPosition === "left" &&
            "absolute top-1/2 -left-2 -translate-x-full -translate-y-1/2 transform",
          tooltipPosition === "right" &&
            "absolute top-1/2 -right-2 translate-x-full -translate-y-1/2 transform",
          // Highlight active state
          isActive && "bg-[#e6f9fb] outline-[#23a1b0]/40"
        )}
      >
        <p className="text-center text-md font-light whitespace-nowrap text-[#23a1b0]">{text}</p>
      </div>
    </div>
  );
};
