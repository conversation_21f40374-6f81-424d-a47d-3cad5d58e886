import { motion } from "framer-motion";
import React from "react";

const SuccessIcon = () => {
  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
      className="relative mx-auto mb-8 h-32 w-32"
    >
      {/* Circle Background */}
      <svg
        className="absolute inset-0"
        viewBox="0 0 119 119"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M59.7733 118.602C92.3948 118.602 118.84 92.1575 118.84 59.536C118.84 26.9146 92.3948 0.469727 59.7733 0.469727C27.1519 0.469727 0.707031 26.9146 0.707031 59.536C0.707031 92.1575 27.1519 118.602 59.7733 118.602Z"
          fill="#f3f8f6"
        />
      </svg>

      {/* Checkmark */}
      <motion.svg
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ delay: 0.5, duration: 0.8, ease: "easeInOut" }}
        className="absolute top-1/2 left-1/2 h-24 w-24 -translate-x-1/2 -translate-y-1/2"
        viewBox="0 0 137 125"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M43.4432 124.19L42.694 122.914C31.2501 103.416 0.839607 62.038 0.53243 61.6224L0.09375 61.0264L10.4544 50.7872L43.2508 73.6879C63.9002 46.8923 83.1647 28.4878 95.7309 17.7402C109.477 5.98353 118.425 0.571127 118.515 0.519262L118.719 0.397461H136.294L134.615 1.89252C91.4397 40.3485 44.6424 122.083 44.1763 122.904L43.4432 124.19Z"
          fill="#85b5a5"
        />
      </motion.svg>
    </motion.div>
  );
};

export default SuccessIcon;
