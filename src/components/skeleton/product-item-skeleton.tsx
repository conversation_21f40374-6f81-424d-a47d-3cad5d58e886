import React from "react";

const ProductItemSkeleton = () => {
  return (
    <div className="relative aspect-[3/4] w-[340px] rounded-[40px] bg-neutral-50 p-3">
      <div className="flex h-full w-full flex-col items-center justify-center p-3">
        {/* Image container - takes up top 70% */}
        <div className="relative w-full flex-[0_0_70%]">
          <div className="h-full w-full animate-pulse rounded-[30px] bg-neutral-200" />
        </div>

        {/* Content container - takes up bottom 30% */}
        <div className="flex w-full flex-[0_0_30%] flex-col items-center justify-end gap-1 pb-3">
          {/* Title skeleton */}
          <div className="h-7 w-3/4 animate-pulse rounded-full bg-neutral-200" />
          {/* Description skeleton */}
          <div className="h-4 w-3/4 animate-pulse rounded-full bg-neutral-200" />
          {/* Price skeleton */}
          <div className="mt-1 h-6 w-1/2 animate-pulse rounded-full bg-neutral-200" />
        </div>
      </div>
    </div>
  );
};

export default ProductItemSkeleton;
