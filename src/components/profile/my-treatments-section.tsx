"use client";
import { useGetBookings } from "@/api/booking-service";
import TreatmentListItem from "@/components/my-treatments/treatment-list-item";
import { motion } from "framer-motion";

const MyTreatmentsSection = () => {
  const {
    bookingDetails,
    bookingDetailsLoading,
    bookingDetailsError,
    bookingDetailsValidating,
    revalidateBookingDetails,
  } = useGetBookings({
    page: 0,
    limit: 100,
  });

  return (
    <div className="min-h-screen w-full bg-white px-4 py-2 sm:px-6 md:px-8">
      <div className="flex w-full flex-col items-center gap-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <h1 className="font-henju mb-1 text-2xl font-normal md:text-3xl">
            Consultations & Treatments
          </h1>
          <p className="text-2md font-light text-neutral-600">
            Manage your appointments and prescriptions.
          </p>
        </motion.div>

        {/* Treatment Cards */}
        <div className="flex min-h-[200px] flex-col items-center justify-center space-y-6">
          {bookingDetailsLoading ? (
            <div className="py-8 text-center text-neutral-400">Loading your treatments...</div>
          ) : bookingDetailsError ? (
            <div className="py-8 text-center text-red-500">
              Failed to load treatments. Please try again.
            </div>
          ) : bookingDetails.length === 0 ? (
            <div className="py-8 text-center text-neutral-400">
              You have no consultations or treatments yet.
            </div>
          ) : (
            bookingDetails.map((booking, index) => (
              <TreatmentListItem
                key={booking._id}
                booking={booking}
                index={index}
                revalidateBookings={revalidateBookingDetails}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default MyTreatmentsSection;
