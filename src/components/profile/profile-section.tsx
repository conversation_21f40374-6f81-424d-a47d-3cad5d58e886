"use client";

import { useRequireAuth } from "@/hooks/use-auth";
import { useAuthStore } from "@/store/auth-store";
import { useToast } from "@/context/toast-context";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MailIcon } from "@/components/icons";
import { Loader2, LogOut, Settings, User } from "lucide-react";
import ToastIcon from "@/components/icons/toast-icon";

export default function ProfileSection() {
  const { user, isLoading } = useRequireAuth();
  const logout = useAuthStore((state) => state.logout);
  const router = useRouter();
  const { showToast } = useToast();

  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isConfirmLogoutOpen, setIsConfirmLogoutOpen] = useState(false);

  const handleLogoutClick = () => {
    setIsConfirmLogoutOpen(true);
  };

  const cancelLogout = () => {
    setIsConfirmLogoutOpen(false);
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      setIsConfirmLogoutOpen(false);
      logout({
        onSuccess: () => {
          showToast("Logged out successfully", "success");
          router.push("/signin");
        },
        onError: (error) => {
          showToast(error.message || "Failed to log out", "error");
        },
      });
    } catch (error) {
      // This will only run if the callbacks don't handle the error
      showToast("Failed to log out", "error");
    } finally {
      setIsLoggingOut(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-[#85b5a5]" />
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen px-10">
        <div className="">
          <div className="overflow-hidden rounded-xl bg-white shadow-sm">
            {/* Profile Header */}
            <div className="flex flex-col items-center gap-6 bg-[#85b5a5]/10 px-6 py-8 md:flex-row md:items-start">
              <div className="bg-primary flex h-24 w-24 items-center justify-center rounded-full text-3xl font-medium text-white">
                {user?.name?.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 text-center md:text-left">
                <h1 className="mb-2 text-2xl font-medium">{user?.name}</h1>
                <div className="mb-4 flex items-center justify-center gap-2 text-gray-600 md:justify-start">
                  <MailIcon />
                  <span>{user?.email}</span>
                </div>
                <div className="flex flex-wrap items-center justify-center gap-2 md:justify-start">
                  {user?.isVerified ? (
                    <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-md font-medium text-green-800">
                      Verified
                    </span>
                  ) : (
                    <span className="inline-flex items-center rounded-full bg-yellow-100 px-3 py-1 text-md font-medium text-yellow-800">
                      Unverified
                    </span>
                  )}
                  <span className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-md font-medium text-blue-800">
                    Member since {new Date(user?.createdAt || "").toLocaleDateString()}
                  </span>
                </div>
              </div>
              <div className="flex flex-row gap-2">
                <Button
                  onClick={handleLogoutClick}
                  variant="outline"
                  withArrow
                  className="flex items-center gap-2 text-2md"
                  disabled={isLoggingOut}
                  isLoading={isLoggingOut}
                  icon={<LogOut className="size-3" />}
                >
                  Logout
                </Button>
              </div>
            </div>

            {/* Profile Content */}
            <div className="p-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                {/* Profile Information */}
                <div className="space-y-6 md:col-span-2">
                  <div>
                    <h2 className="mb-4 text-xl font-medium">Profile Information</h2>
                    <div className="space-y-4 rounded-lg bg-gray-50 p-6">
                      <div>
                        <label className="mb-1 block text-2md font-medium text-gray-700">Name</label>
                        <div className="flex items-center gap-2 rounded-md border border-gray-200 bg-white p-3">
                          <User className="h-5 w-5 text-gray-400" />
                          <span>{user?.name}</span>
                        </div>
                      </div>
                      <div>
                        <label className="mb-1 block text-2md font-medium text-gray-700">
                          Email
                        </label>
                        <div className="flex items-center gap-2 rounded-md border border-gray-200 bg-white p-3">
                          <MailIcon />
                          <span>{user?.email}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h2 className="mb-4 text-xl font-medium">Account Settings</h2>
                    <div className="space-y-4 rounded-lg bg-gray-50 p-6">
                      <Button
                        variant="outline"
                        fullWidth
                        className="flex items-center justify-start gap-2"
                        onClick={() => showToast("Feature coming soon!", "info")}
                      >
                        <Settings className="h-4 w-4" />
                        Change Password
                      </Button>
                      {!user?.isVerified && (
                        <Button
                          variant="outline"
                          className="flex w-full items-center justify-start gap-2"
                          onClick={() =>
                            router.push(`/verify?email=${encodeURIComponent(user?.email || "")}`)
                          }
                        >
                          <MailIcon className="h-4 w-4" />
                          Verify Email
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Activity Summary */}
                <div>
                  <h2 className="mb-4 text-xl font-medium">Activity Summary</h2>
                  <div className="space-y-4 rounded-lg bg-gray-50 p-6">
                    <div className="py-8 text-center">
                      <p className="mb-4 text-gray-500">No recent activity</p>
                      <Button
                        onClick={() => router.push("/treatments")}
                        className="bg-[#85b5a5] hover:bg-[#6a9189]"
                      >
                        Browse Treatments
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Logout Confirmation Dialog */}
      {isConfirmLogoutOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="mx-4 w-full max-w-md rounded-xl bg-white p-6">
            <h2 className="font-henju mb-4 text-xl">Logout</h2>
            <div className="mb-6 flex items-center gap-4">
              <div className="flex-shrink-0">
                <ToastIcon size="xs" type="warning" />
              </div>
              <p className="text-gray-700">Are you sure you want to logout?</p>
            </div>
            <div className="flex flex-col gap-3 sm:flex-row">
              <Button
                type="button"
                variant="outline"
                className="font-henju w-full min-w-32 truncate border border-[#85b5a5] bg-white py-2 pr-2 pl-5 text-2md text-[#85b5a5] hover:bg-[#85b5a5] hover:text-white"
                onClick={cancelLogout}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="outline"
                className="font-henju w-full min-w-32 truncate border border-[#85b5a5] bg-[#85b5a5]/10 py-2 pr-2 pl-5 text-2md text-[#85b5a5] hover:bg-[#85b5a5] hover:text-white"
                onClick={handleLogout}
              >
                Logout
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
