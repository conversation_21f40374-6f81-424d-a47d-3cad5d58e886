import React from "react";
import Image from "next/image";
import { useBagStore } from "@/store/bag-store";

const NavBagIcon = () => {
  const items = useBagStore((state) => state.items);

  return (
    <div className="relative">
      {items.length > 0 && (
        <p className="font-dm-sans absolute -top-2 -right-2 flex size-4 items-center justify-center rounded-full bg-[#23a1b0] text-[10px] font-medium text-white">
          {items.length}
        </p>
      )}
      <Image
        src="/icons/bag-icon.svg"
        alt="Bag Image"
        width={16}
        height={16}
        className="size-5 text-black"
      />
    </div>
  );
};

export default NavBagIcon;
