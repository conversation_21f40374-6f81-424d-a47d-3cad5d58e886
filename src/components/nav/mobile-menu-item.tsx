"use client";
import { MenuItem, SubMenuItem } from "@/types/navigation";
import { DropdownArrow } from "./dropdown-arrow";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { on } from "events";
import Image from "next/image";

interface MobileMenuItemProps {
  item: MenuItem;
  index: number;
  activeDropdown: number | null;
  onDropdownToggle: (index: number) => void;
  closeMenu: () => void;
}

export const MobileMenuItem = ({
  item,
  index,
  activeDropdown,
  onDropdownToggle,
  closeMenu,
}: MobileMenuItemProps) => {
  const router = useRouter();
  const [activeNestedDropdown, setActiveNestedDropdown] = useState<number | null>(null);

  const toggleNestedDropdown = (subIndex: number) => {
    setActiveNestedDropdown(activeNestedDropdown === subIndex ? null : subIndex);
  };

  const renderSubItem = (subItem: SubMenuItem, subIndex: number, isNested = false) => {
    const hasNestedItems = subItem.subItems && subItem.subItems.length > 0;

    return (
      <div key={subIndex} className={cn(isNested ? "pl-4" : "")}>
        <button
          onClick={() => {
            if (hasNestedItems) {
              toggleNestedDropdown(subIndex);
            } else {
              router.push(subItem.href);
              onDropdownToggle(index);
              closeMenu();
            }
          }}
          className={cn(
            "text-2md flex w-full items-center justify-between py-3 text-left text-gray-700 transition-all duration-200 hover:text-[#23a1b0]",
            "transform transition-transform",
            activeDropdown === index ? "translate-x-0 opacity-100" : "-translate-x-2 opacity-0",
            `delay-[${subIndex * 50}ms]`
          )}
        >
          <div className="flex flex-row items-center gap-4">
            <Image
              src={`/images/header/icons/icon${subIndex + 1}.svg`}
              alt={subItem.label}
              width={20}
              height={20}
              className="size-5"
            />
            <span>{subItem.label}</span>
          </div>
          {hasNestedItems && <DropdownArrow isOpen={activeNestedDropdown === subIndex} size="sm" />}
        </button>

        {/* Nested subitems */}
        {hasNestedItems && (
          <div
            className={cn(
              "overflow-hidden pl-4 transition-all duration-300 ease-in-out",
              activeNestedDropdown === subIndex ? "max-h-[300px] opacity-100" : "max-h-0 opacity-0"
            )}
          >
            {subItem.subItems?.map((nestedItem, nestedIndex) => (
              <button
                key={nestedIndex}
                onClick={() => router.push(nestedItem.href)}
                className={cn(
                  "text-2md block w-full py-2 text-left text-gray-600 transition-all duration-200 hover:text-[#23a1b0]",
                  "transform transition-transform",
                  activeNestedDropdown === subIndex
                    ? "translate-x-0 opacity-100"
                    : "-translate-x-2 opacity-0",
                  `delay-[${nestedIndex * 30}ms]`
                )}
              >
                {nestedItem.label}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div key={index}>
      <button
        onClick={() => {
          if (item.hasDropdown) {
            onDropdownToggle(index);
          } else {
            router.push(item.href);
            closeMenu();
          }
        }}
        className="flex w-full items-center justify-between border-b border-gray-100 px-2 py-4"
      >
        <div className="text-2md font-normal text-black">{item.label}</div>
        {item.hasDropdown && <DropdownArrow isOpen={activeDropdown === index} />}
      </button>
      <div
        className={cn(
          "relative overflow-hidden transition-all duration-300 ease-in-out",
          activeDropdown === index ? "max-h-[800px] opacity-100" : "max-h-0 opacity-0"
        )}
      >
        {/* Add backdrop blur effect */}
        <div className="absolute inset-0 z-0 bg-white/80 backdrop-blur-[10px]" />
        <div className="relative z-10 px-4">
          {item.subItems?.map((subItem, subIndex) => renderSubItem(subItem, subIndex))}
        </div>
      </div>
    </div>
  );
};
