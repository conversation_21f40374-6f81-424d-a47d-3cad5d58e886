import { cn } from "@/lib/utils";
import { MenuItem } from "@/types/navigation";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useRef, useState, useEffect } from "react";

interface DesktopMenuItemProps {
  item: MenuItem;
  index: number;
  activeDropdown: number | null;
  onDropdownToggle: (index: number) => void;
}

export const DesktopMenuItem = ({
  item,
  index,
  activeDropdown,
  onDropdownToggle,
}: DesktopMenuItemProps) => {
  const pathname = usePathname();
  const [isHovering, setIsHovering] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const menuItemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isHovering && item.hasDropdown) {
      if (activeDropdown !== index) {
        onDropdownToggle(index);
      }
    }

    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
        hoverTimeoutRef.current = null;
      }
    };
  }, [isHovering, index, item.hasDropdown, onDropdownToggle, activeDropdown]);

  const handleMouseEnter = () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHovering(false);
      if (activeDropdown === index) {
        onDropdownToggle(index);
      }
    }, 150);
  };

  if (!item.hasDropdown) {
    return (
      <Link href={item.href} className="group flex items-center">
        <div
          className={`${pathname === item.href ? "text-[#23a1b0]" : "text-black"} text-2md max-w-40 truncate font-light transition-colors duration-200 group-hover:text-[#23a1b0]`}
        >
          {item.label}
        </div>
      </Link>
    );
  }

  return (
    <div
      ref={menuItemRef}
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        onClick={() => onDropdownToggle(index)}
        className="group text-2md flex max-w-36 items-center truncate font-thin"
      >
        <div
          className={cn(
            "text-2md max-w-36 truncate leading-normal font-thin transition-colors duration-200 group-hover:text-[#23a1b0]",
            activeDropdown === index ? "text-[#23a1b0]" : "text-black"
          )}
        >
          {item.label}
        </div>
        {item.hasDropdown && (
          <div className="relative ml-1 size-4 overflow-hidden">
            <ChevronDown
              size={16}
              className={cn(
                "transform transition-transform duration-300 ease-in-out group-hover:text-[#23a1b0]",
                activeDropdown === index ? "rotate-180 text-[#23a1b0]" : "text-black"
              )}
            />
          </div>
        )}
      </button>
      <div
        className={cn(
          "absolute top-full left-0 z-[100] mt-[29px] h-[540px] w-[300px] overflow-hidden rounded-br-[28px] rounded-bl-[28px]",
          "origin-top transition-all duration-300 ease-in-out",
          activeDropdown === index
            ? "translate-y-0 scale-100 transform opacity-100"
            : "pointer-events-none -translate-y-2 scale-95 transform opacity-0"
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Add a background div */}
        <div
          className="absolute inset-0 border border-gray-100/50 bg-white/80 backdrop-blur-[50px]"
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
        <div className="relative grid grid-cols-1 px-6 py-1">
          {item.subItems?.map((subItem, subIndex) => (
            <div key={subIndex}>
              <Link
                href={subItem.href}
                className={cn(
                  "text-2md flex w-full items-center px-4 py-2 text-[15px] text-gray-700",
                  "font-light transition-all duration-200 hover:font-normal hover:text-[#23a1b0]",
                  "group relative",
                  activeDropdown === index ? "animate-fadeIn" : "opacity-0",
                  `animation-delay-${subIndex * 100}`
                )}
              >
                <div className="z-10 flex flex-row items-center gap-4">
                  <Image
                    src={`/images/header/icons/icon${subIndex + 1}.svg`}
                    alt={subItem.label}
                    width={20}
                    height={20}
                    className="size-5"
                  />
                  <span className="relative">{subItem.label}</span>
                </div>
              </Link>
              {subItem.subItems && (
                <div className="grid grid-cols-1 pl-11">
                  {subItem.subItems.map((nestedItem, nestedIndex) => (
                    <Link
                      key={nestedIndex}
                      href={nestedItem.href}
                      className={cn(
                        "text-2md flex w-full items-center px-2 py-1 text-[12px] text-gray-600",
                        "transition-all duration-200 hover:text-[#23a1b0]",
                        "group relative",
                        activeDropdown === index ? "animate-fadeIn" : "opacity-0",
                        `animation-delay-${subIndex * 100 + nestedIndex * 50}`
                      )}
                    >
                      <span className="relative z-10">{nestedItem.label}</span>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
