"use client";

import {
  createAddress,
  updateAddress,
  useGetAddressById,
  useGetAddresses,
} from "@/api/address-service";
import { useToast } from "@/context/toast-context";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "../ui/button";
import Dialog from "../ui/dialog";

// Define the validation schema using Zod
const addressSchema = z.object({
  contactName: z.string().min(1, { message: "Name is required" }),
  street: z.string().optional(),
  state: z.string().min(1, { message: "State is required" }),
  city: z.string().min(1, { message: "City is required" }),
  zipCode: z.string().min(1, { message: "Zip code is required" }),
});

// Infer the type from the schema
type AddressFormData = z.infer<typeof addressSchema>;

interface AddAddressDialogProps {
  isOpen: boolean;
  onClose: () => void;
  addressToEdit?: string; // ID of address to edit, if any
  className?: string;
}

export const AddAddressDialog: React.FC<AddAddressDialogProps> = ({
  isOpen,
  onClose,
  addressToEdit,
  className,
}) => {
  const { showToast } = useToast();
  const { address, revalidateAddress, addressLoading, addressError } =
    useGetAddressById(addressToEdit);
  const { revalidateAddresses } = useGetAddresses({
    page: 1,
    limit: 10,
  });
  // Get the address to edit if provided
  const addressData = addressToEdit ? address : undefined;
  // Initialize react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: addressData
      ? {
          contactName: addressData.contactName,
          street: addressData.street || "",
          state: addressData.state,
          city: addressData.city,
          zipCode: addressData.zipCode,
        }
      : {
          contactName: "",
          street: "",
          state: "",
          city: "",
          zipCode: "",
        },
  });

  // Update form values when addressData changes
  React.useEffect(() => {
    if (addressData) {
      // Set address type

      // Reset form with address data
      reset({
        contactName: addressData.contactName,
        street: addressData.street || "",
        state: addressData.state,
        city: addressData.city,
        zipCode: addressData.zipCode,
      });
    } else {
      // Reset form to empty values when not in edit mode
      reset({
        contactName: "",
        street: "",
        state: "",
        city: "",
        zipCode: "",
      });
    }
  }, [addressData, reset]);

  // Handle form submission
  const onSubmit = async (data: AddressFormData) => {
    try {
      if (addressToEdit && addressData) {
        const updatedAddress = {
          ...data,
        };

        await updateAddress(addressToEdit, updatedAddress)
          .then(() => {
            showToast("Address updated successfully", "success");
          })
          .catch((error) => {
            showToast("Failed to update address", "error");
          });
        revalidateAddresses();
      } else {
        const newAddress = {
          contactName: data.contactName,

          street: data.street,
          state: data.state,
          city: data.city,
          zipCode: data.zipCode,
          isDefault: false,
        };

        await createAddress(newAddress);
        showToast("Address added successfully", "success");
        revalidateAddresses();
      }

      // Reset form and close dialog
      reset();
      onClose();
    } catch (error) {
      showToast(error instanceof Error ? error.message : "An error occurred", "error");
    } finally {
      revalidateAddresses();
    }
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title={addressToEdit ? "Edit Address" : "Add New Address"}
      className={cn("max-w-[400px]", className)}
    >
      <div className="w-full">
        {/* Address Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Address Field */}
          <div className="relative h-12 w-full">
            <div
              className="relative flex h-12 w-full flex-col border-b pb-0 transition-colors duration-200 focus-within:border-[#24A1B0]"
              style={{ borderColor: errors.contactName ? "#cd1010" : "#e6e6e6" }}
            >
              <label className="absolute top-1 left-2 flex">
                <span className="text-2md font-normal text-neutral-600">Contact Name</span>
                <span className="text-2md font-normal text-[#cd1010]">*</span>
              </label>
              <input
                type="text"
                className="text-2md h-full w-full bg-transparent px-2 pt-6 font-light text-gray-700 focus:outline-none"
                {...register("contactName")}
              />
            </div>
            {errors.contactName && (
              <p className="mt-1 text-xs text-[#cd1010]">{errors.contactName.message}</p>
            )}
          </div>

          {/* Street Field */}
          <div className="relative h-12 w-full">
            <div className="relative flex h-12 w-full flex-col border-b border-[#e6e6e6] pb-0 transition-colors duration-200 focus-within:border-[#24A1B0]">
              <label className="absolute top-1 left-2 flex">
                <span className="text-2md font-normal text-neutral-600">Street</span>
              </label>
              <input
                type="text"
                className="text-2md h-full w-full bg-transparent px-2 pt-6 font-light text-gray-700 focus:outline-none"
                {...register("street")}
              />
            </div>
          </div>

          {/* State Field */}
          <div className="relative h-12 w-full">
            <div
              className="relative flex h-12 w-full flex-col border-b pb-0 transition-colors duration-200 focus-within:border-[#24A1B0]"
              style={{ borderColor: errors.state ? "#cd1010" : "#e6e6e6" }}
            >
              <label className="absolute top-1 left-2 flex">
                <span className="text-2md font-normal text-neutral-600">State</span>
                <span className="text-2md font-normal text-[#cd1010]">*</span>
              </label>
              <select
                className="text-2md h-full w-full appearance-none bg-transparent px-2 pt-6 font-light text-gray-700 focus:outline-none"
                {...register("state")}
              >
                <option value=""></option>
                <option value="AL">Alabama</option>
                <option value="AK">Alaska</option>
                <option value="AZ">Arizona</option>
                <option value="CA">California</option>
                <option value="CO">Colorado</option>
                <option value="FL">Florida</option>
                <option value="GA">Georgia</option>
                <option value="HI">Hawaii</option>
                <option value="NY">New York</option>
                <option value="TX">Texas</option>
                <option value="WA">Washington</option>
                {/* Add more states as needed */}
              </select>
            </div>
            {errors.state && <p className="mt-1 text-xs text-[#cd1010]">{errors.state.message}</p>}
          </div>

          {/* City Field */}
          <div className="relative h-12 w-full">
            <div
              className="relative flex h-12 w-full flex-col border-b pb-0 transition-colors duration-200 focus-within:border-[#24A1B0]"
              style={{ borderColor: errors.city ? "#cd1010" : "#e6e6e6" }}
            >
              <label className="absolute top-1 left-2 flex">
                <span className="text-2md font-normal text-neutral-600">City</span>
                <span className="text-2md font-normal text-[#cd1010]">*</span>
              </label>
              <select
                className="text-2md h-full w-full appearance-none bg-transparent px-2 pt-6 font-light text-gray-700 focus:outline-none"
                {...register("city")}
              >
                <option value=""></option>
                <option value="New York">New York</option>
                <option value="Los Angeles">Los Angeles</option>
                <option value="Chicago">Chicago</option>
                <option value="Houston">Houston</option>
                <option value="Phoenix">Phoenix</option>
                <option value="Philadelphia">Philadelphia</option>
                <option value="San Antonio">San Antonio</option>
                <option value="San Diego">San Diego</option>
                <option value="Dallas">Dallas</option>
                <option value="San Jose">San Jose</option>
                {/* Add more cities as needed */}
              </select>
            </div>
            {errors.city && <p className="mt-1 text-xs text-[#cd1010]">{errors.city.message}</p>}
          </div>

          {/* Zip Code Field */}
          <div className="relative h-12 w-full">
            <div
              className="relative flex h-12 w-full flex-col border-b pb-0 transition-colors duration-200 focus-within:border-[#24A1B0]"
              style={{ borderColor: errors.zipCode ? "#cd1010" : "#e6e6e6" }}
            >
              <label className="absolute top-1 left-2 flex">
                <span className="text-2md font-normal text-neutral-600">Zip Code</span>
                <span className="text-2md font-normal text-[#cd1010]">*</span>
              </label>
              <input
                type="text"
                className="text-2md h-full w-full bg-transparent px-2 pt-6 font-light text-gray-700 focus:outline-none"
                {...register("zipCode")}
              />
            </div>
            {errors.zipCode && (
              <p className="mt-1 text-xs text-[#cd1010]">{errors.zipCode.message}</p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 pt-4 sm:flex-row">
            <Button
              type="button"
              variant="primary"
              withArrow
              className="font-henju text-2md hover:bg-primary w-full min-w-32 truncate border bg-[#C9DFDD] py-2 pr-2 pl-5 hover:text-white"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              withArrow
              className="font-henju text-2md w-full min-w-32 truncate py-2 pr-2 pl-5"
              isLoading={isSubmitting}
            >
              {addressToEdit ? "Update Address" : "Add Address"}
            </Button>
          </div>
        </form>
      </div>
    </Dialog>
  );
};

export default AddAddressDialog;
