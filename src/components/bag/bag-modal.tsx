"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import DropdownMenu from "../ui/dropdown-menu";
import Dialog from "../ui/dialog";
import Bag from "./bag";
import { useIsMobile } from "@/hooks/use-media-query";

interface BagModalProps {
  trigger: React.ReactNode;
  className?: string;
}

const BagModal: React.FC<BagModalProps> = ({ trigger, className }) => {
  const isMobile = useIsMobile();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleTriggerClick = () => {
    if (isMobile) {
      setIsDialogOpen(true);
    }
  };

  return (
    <>
      {/* For mobile: Dialog */}
      {isMobile ? (
        <>
          <div onClick={handleTriggerClick} className="cursor-pointer">
            {trigger}
          </div>
          <Dialog
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
            title="My Bag"
            className="h-full w-full max-w-full rounded-t-xl rounded-b-none sm:h-auto sm:rounded-xl 2xl:max-w-[500px]"
          >
            <Bag
              isModal={true}
              className="h-full max-h-[70vh] overflow-y-auto"
              onClose={() => setIsDialogOpen(false)}
            />
          </Dialog>
        </>
      ) : (
        /* For desktop: Dropdown */
        <DropdownMenu
          trigger={trigger}
          position="bottom"
          align="end"
          width="w-full md:w-[463px]"
          className={cn("", className)}
        >
          <Bag isModal={true} className="h-full" />
        </DropdownMenu>
      )}
    </>
  );
};

export default BagModal;
