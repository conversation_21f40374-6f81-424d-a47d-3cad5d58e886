import { useRouter } from "next/navigation";
import React from "react";
import { But<PERSON> } from "../ui/button";

const BackToHomeButton = () => {
  const router = useRouter();
  return (
    <Button
      onClick={() => router.push("/")}
      withArrow
      variant="outline"
      className="text-2md w-60 min-w-32 truncate border-[#4fa097] py-3 pr-2 pl-5 text-[#4fa097] outline-[#4fa097]"
      iconColor="#4fa097"
    >
      Back to Home
    </Button>
  );
};

export default BackToHomeButton;
