
export const treatments = [
  {
    id: 1,
    title: "Anti-aging treatment",
    type: "Online consultation",
    status: {
      type: "awaiting",
      label: "Awaiting confirmation",
      className: "bg-[#fef5ed] text-[#b93814] outline-[#f8dbaf]",
      dotColor: "bg-[#ef681f]",
    },
    date: "Tue, 4 Mar, 2025",
    time: "01:30 pm",
    description: "You're almost there!",
    subDescription: "Your appointment is scheduled with our qualified anti-aging specialist",
    doctorImage: null,
    isConfirm: false,
    isCompleted: false,
    completePresentage: 0,
  },
  {
    id: 2,
    title: "Hair loss treatment",
    type: "Online consultation",
    status: {
      type: "confirmed",
      label: "Confirmed",
      className: "bg-[#ecfcf2] text-[#057647] outline-[#aaefc6]",
      dotColor: "bg-[#17b169]",
    },
    date: "Tue, 4 Mar, 2025",
    time: "01:30 pm",
    description: "Dr. <PERSON>",
    subDescription: "<PERSON><PERSON>, is a specialist in anti-aging medicine",
    doctorImage: null,
    isConfirm: true,
    isCompleted: false,
    completePresentage: 60,
  },
  {
    id: 3,
    title: "Hair loss treatment",
    type: "Online consultation",
    status: {
      type: "confirmed",
      label: "Confirmed",
      className: "bg-[#ecfcf2] text-[#057647] outline-[#aaefc6]",
      dotColor: "bg-[#17b169]",
    },
    date: "Tue, 4 Mar, 2025",
    time: "01:30 pm",
    description: "Dr. Fiona McKenzie",
    subDescription: "MBBS, is a specialist in anti-aging medicine",
    doctorImage: null,
    isConfirm: true,
    isCompleted: true,
    completePresentage: 100,
  },
];

export const treatmentDetails = {
  1: {
    id: 1,
    title: "Anti-aging treatment",
    type: "Online consultation",
    status: {
      type: "awaiting",
      label: "Waiting for payment",
      className: "bg-[#fef5ed] text-[#b93814] outline-[#f8dbaf]",
      dotColor: "bg-[#ef681f]",
    },
    doctor: {
      name: "Dr. Fiona McKenzie",
      qualification: "MBBS, is a specialist in anti-aging medicine",
      image: "https://placehold.co/40x40",
    },
    date: "Tue, 4 Mar, 2025",
    time: "01:30 pm",

    treatmentPlan: [
      {
        name: "GHRP6 Cream + CJC1295 30mL",
        concentration: "400mcg/mL + 600mcg/mL",
        category: "Anti-aging",
        instructions: "Once daily at night on an empty stomach for 5 days a week.",
        price: 304,
        isSelected: true,
      },
      {
        name: "Ageless Radiance",
        concentration: null,
        category: "Hair loss",
        instructions: "Once daily at night on an empty stomach for 5 days a week",
        price: 474,
        isSelected: false,
      },
    ],
  },
  // Add more treatment details as needed
};


export const CategoryDetail = {
  title: "Skin Care",
  description:
    "Our skincare treatments are designed to nourish, hydrate, and protect your skin—leaving it smooth, radiant, and healthy. Using gentle, effective ingredients and expert-backed routines, we help you care for your skin from the inside out.",
  image: "/images/treatments/image1.jpg",
  subCategories: [
    {
      title: "Acne",
      description: "A soothing acne treatment that clears breakouts, calms redness, and supports clearer, healthier skin—gently and effectively.",
      image: "/images/treatments/image2.jpg",
      products: [
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 423,
          image: "/images/products/product1.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 123,
          image: "/images/products/product2.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 23,
          image: "/images/products/product3.png",
        },
      ],
    },
    {
      title: "Pigmentation",
      description: "A soothing acne treatment that clears breakouts, calms redness, and supports clearer, healthier skin—gently and effectively.",
      image: "/images/treatments/image3.jpg",
      products: [
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 423,
          image: "/images/products/product1.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 123,
          image: "/images/products/product2.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 23,
          image: "/images/products/product3.png",
        },
      ],
    }, {
      title: "Wrinkles & Aging",
      description: "A soothing acne treatment that clears breakouts, calms redness, and supports clearer, healthier skin—gently and effectively.",
      image: "/images/treatments/image4.jpg",
      products: [
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 423,
          image: "/images/products/product1.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 123,
          image: "/images/products/product2.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 23,
          image: "/images/products/product3.png",
        },
      ],
    }
    , {
      title: "Excessive Sweating",
      description: "A soothing acne treatment that clears breakouts, calms redness, and supports clearer, healthier skin—gently and effectively.",
      image: "/images/treatments/image5.jpg",
      products: [
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 423,
          image: "/images/products/product1.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 123,
          image: "/images/products/product2.png",
        },
        {
          name: "Ageless Radiance",
          description: "Clinically-backed formulas for radiant skin.",
          price: 23,
          image: "/images/products/product3.png",
        },
      ],
    }

  ],
  suggestProducts: [
    {
      name: "Ageless Radiance",
      description: "Clinically-backed formulas for radiant skin.",
      price: 423,
      main_image: "/images/products/product1.png",
    },
    {
      name: "Ageless Radiance",
      description: "Clinically-backed formulas for radiant skin.",
      price: 123,
      main_image: "/images/products/product2.png",
    },
    {
      name: "Ageless Radiance",
      description: "Clinically-backed formulas for radiant skin.",
      price: 23,
      main_image: "/images/products/product3.png",
    },
    {
      name: "Ageless Radiance",
      description: "Clinically-backed formulas for radiant skin.",
      price: 23,
      main_image: "/images/products/product1.png",
    },
    {
      name: "Ageless Radiance",
      description: "Clinically-backed formulas for radiant skin.",
      price: 23,
      main_image: "/images/products/product2.png",
    },
  ]
};