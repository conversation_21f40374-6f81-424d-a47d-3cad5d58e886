"use client";

import { useGetPublicProductCategories } from "@/api/category-service";
import { InfiniteProductList } from "@/components/products/infinite-product-list";
import { ShopFilterV2 } from "@/components/shop/shop-filter-v2";
import { TabButton } from "@/components/ui/tab-button";
import { TabItem } from "@/components/ui/tab-group";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

const InfiniteShoppingPage = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const {
    productCategories,
    productCategoriesLoading,
    productCategoriesError,
    productCategoriesEmpty,
  } = useGetPublicProductCategories({ page: 0, limit: 100 });

  // Use a single state object for all filter criteria to reduce re-renders
  const [filterCriteria, setFilterCriteria] = useState({
    activeTab: "",
    category: "",
    filter: "",
    sort: "",
    search: "",
  });

  // Transform categories into tab format
  const tabs: TabItem[] = useMemo(() => {
    if (productCategoriesEmpty || productCategoriesError || !productCategories.length) {
      // Fallback tabs if categories can't be loaded
      return [];
    }

    const mappedCategories = productCategories.map((category) => ({
      id: category._id || category.id,
      label: category.name,
    }));

    return [{ id: "all", label: "All" }, ...mappedCategories];
  }, [productCategories, productCategoriesEmpty, productCategoriesError]);

  // Set initial active tab once categories are loaded
  useEffect(() => {
    // We don't set an initial tab by default, allowing tabs to be initially unselected
    // If you want to set an initial tab, uncomment the code below
    if (tabs.length > 0 && !filterCriteria.activeTab) {
      setFilterCriteria((prev) => ({
        ...prev,
        activeTab: tabs[0].id,
        category: tabs[0].id,
      }));
    }
  }, [tabs, filterCriteria.activeTab]);

  // Memoize event handlers to prevent unnecessary re-renders
  const handleTabChange = useCallback(
    (tabId: string) => {
      // if (tabId === filterCriteria.activeTab) {
      //   setFilterCriteria((prev) => ({
      //     ...prev,
      //     activeTab: "",
      //     category: "",
      //   }));
      //   return;
      // }

      setFilterCriteria((prev) => ({
        ...prev,
        activeTab: tabId,
        category: tabId,
      }));
    },
    [filterCriteria.activeTab]
  );

  const handleFilterChange = useCallback((filter: string) => {
    setFilterCriteria((prev) => ({ ...prev, filter }));
  }, []);

  const handleSortChange = useCallback((sort: string) => {
    setFilterCriteria((prev) => ({ ...prev, sort }));
  }, []);

  const handleSearch = useCallback((query: string) => {
    setFilterCriteria((prev) => ({ ...prev, search: query }));
  }, []);

  // Render category tabs with proper error handling
  const renderCategoryTabs = () => {
    if (productCategoriesLoading) {
      return (
        <div className={"flex flex-wrap gap-4"}>
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="min-w-[120px] flex-1">
              <div className="h-14 w-full animate-pulse rounded-full bg-neutral-100"></div>
            </div>
          ))}
        </div>
      );
    }

    if (productCategoriesError) {
      return (
        <div className="flex w-full justify-center">
          <div className="text-red-500">
            Error loading categories: {productCategoriesError?.message || "Unknown error"}
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full max-w-7xl px-4 sm:px-6 lg:px-8 2xl:max-w-[1728px]">
        <div className="flex items-center">
          <div
            ref={scrollContainerRef}
            className="scrollbar-hide flex snap-x snap-mandatory gap-6 overflow-x-auto px-8 py-4"
          >
            {tabs.map((tab) => (
              <div key={tab.id} className="flex-none snap-start">
                <TabButton
                  label={tab.label}
                  isActive={filterCriteria.activeTab === tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className="min-w-[120px] px-4"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="mt-20 flex min-h-screen flex-col items-center gap-4 bg-white px-4 sm:px-6 md:mt-24 md:gap-4 lg:px-8">
      {/* Tab Group - With loading state */}
      {renderCategoryTabs()}

      {/* Shop Filter V2 - Compact version */}
      <div className="max-w-7xl">
        {useMemo(
          () => (
            <ShopFilterV2
              initialFilter={filterCriteria.filter}
              initialSort={filterCriteria.sort}
              onFilterChange={handleFilterChange}
              onSortChange={handleSortChange}
              onSearch={handleSearch}
            />
          ),
          [
            filterCriteria.filter,
            filterCriteria.sort,
            handleFilterChange,
            handleSortChange,
            handleSearch,
          ]
        )}
      </div>

      {/* Infinite Product List - Using our new SWR-powered component with infinite scrolling */}
      <InfiniteProductList
        category={filterCriteria.category}
        limit={8}
        searchQuery={filterCriteria.search}
      />
    </div>
  );
};

export default InfiniteShoppingPage;
