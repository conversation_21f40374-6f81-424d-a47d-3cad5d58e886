"use client";
import { useParams } from "next/navigation";

import { useGetProductById } from "@/api/product-service";
import { ProductDetail } from "@/components/products/product-detail";
import ProductDetailsSkeletonPage from "@/components/products/product-details-skeleton-page";
import SuggestedProducts from "@/components/products/suggested-products";
import Image from "next/image";
import IngredientsListSection from "@/components/products/ingredients-list-section";

const ProductDetailPage = () => {
  const { id } = useParams();
  const { product, productLoading, productError, productEmpty } = useGetProductById(id as string);

  if (!id) {
    return <div>Product not found.</div>;
  }

  if (productLoading) {
    return <ProductDetailsSkeletonPage />;
  }

  return (
    <div className="mt-16 flex min-h-screen w-full flex-col items-center gap-4 bg-white px-4 sm:px-6 md:mt-28 md:gap-16 lg:px-16">
      {/* Product Detail Component */}
      <ProductDetail
        product={product}
        isLoading={productLoading}
        error={productError}
        isEmpty={productEmpty}
      />

      {/* Clinical Results and Suggested Products */}
      <ClinicalResultDetails details={product?.clinicalDetails} />
      <IngredientsListSection />
      <SuggestedProducts products={product?.relatedProducts} />
    </div>
  );
};

export default ProductDetailPage;

function ClinicalResultDetails({ details }: any) {
  if (!details || details.length === 0) {
    return null;
  }

  const clinicalResults = details[0];

  return (
    <div className="relative flex w-full flex-col gap-8 overflow-hidden rounded-[30px] border border-neutral-50 bg-[#fffaf7] p-4 md:gap-12 md:rounded-[50px] md:px-8 md:py-12 lg:flex-row lg:gap-12">
      <div className="flex w-full flex-col gap-4 lg:w-1/2">
        <div className="gap-3">
          <h1 className="font-henju text-3xl font-normal text-black">Clinical Result</h1>
          <p className="text-2md max-w-[579px] font-light text-[#535353]">
            {clinicalResults.title}
          </p>
        </div>
        <div className="flex flex-col gap-4">
          {clinicalResults.contents.map((result, index) => (
            <div
              key={index}
              className="text-2md flex min-h-[80px] w-full max-w-[616px] items-center justify-start rounded-full bg-white px-4 py-4 font-light text-black md:px-8 md:text-[16px]"
            >
              {result}
            </div>
          ))}
        </div>
      </div>
      <div className="flex w-full flex-col gap-4 sm:flex-row md:gap-6 lg:w-1/2">
        {clinicalResults.images.map((image, index) => (
          <div
            key={index}
            className="relative h-[280px] w-full sm:h-[320px] sm:w-1/2 md:h-[400px] lg:h-[420px]"
          >
            <Image
              src={image?.imageUrl || "/images/products/product1.png"}
              alt={`Clinical result ${index + 1}`}
              fill
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
              priority={index === 0}
              className="rounded-[30px] object-cover md:rounded-[50px]"
              quality={90}
            />
            <div className="absolute bottom-2 left-1/2 h-12 min-w-44 -translate-1/2 transform rounded-full bg-white/50 outline-1 outline-white backdrop-blur-[10px]">
              <p className="text-2md absolute top-1/2 left-1/2 -translate-1/2 transform text-center font-normal text-black">
                {image.title || `Image ${index + 1}`}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
