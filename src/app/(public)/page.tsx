import ContactUsSection from "@/components/contact-us/contact-us-section";
import ChatBotSection from "@/components/home-sections/chat-bot/chat-bot-section";
import CheckBMISection from "@/components/home-sections/check-bmi-section/check-bmi-section";
import CustomMadeForYouHomeSection from "@/components/home-sections/custom-made-for-you-home-section";
import DataDrivenSection from "@/components/home-sections/data-driven-section/data-driven-section";
import FAQSection from "@/components/home-sections/faq-section";
import Header from "@/components/home-sections/header/header";
import HeaderRibbon from "@/components/home-sections/header/header-ribbon";
import HeaderV2 from "@/components/home-sections/header/header-v2";
import HeaderV3 from "@/components/home-sections/header/header-v3";
import HowItWorksSection from "@/components/home-sections/how-it-works-section/how-it-works-section";
import OurApproachHomeSection from "@/components/home-sections/our-approach-home-section";
import PersonalizedForYouSection from "@/components/home-sections/personalized-for-you-section/personalized-for-you-section";
import ShopProductsSection from "@/components/home-sections/shop-products-section/shop-products-section";
import SkincareHomeSection from "@/components/home-sections/skincare-home-section";
import SolutionsJustForYouSection from "@/components/home-sections/solutions-just-for-you-section/solutions-just-for-you-section";
import TreatmentHomeSection from "@/components/home-sections/treatment-home-section";
import WhyWeLoved from "@/components/home-sections/why-we-loved-section/why-we-loved-section";

export default function Home() {
  return (
    <main className="relative mt-16 flex flex-col gap-12 md:mt-20 md:gap-16 lg:gap-24">
      <ChatBotSection />
      <HeaderV3 />
      {/* <HeaderV2 /> */}
      {/* <Header /> */}
      <HeaderRibbon />
      <ShopProductsSection />
      <DataDrivenSection />
      <HowItWorksSection />
      <PersonalizedForYouSection />
      <SolutionsJustForYouSection />
      <WhyWeLoved />
      <CheckBMISection />
      <FAQSection />
      <div className="flex w-full items-center justify-center">
        <ContactUsSection />
      </div>
    </main>
  );
}
