import Footer from "@/components/footer";
import NavBar from "@/components/nav/nav-bar";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Revolved | Modern Healthcare Solutions",
  description:
    "Access personalized treatments for anti-aging, weight loss, skin care, and more. Connect with qualified healthcare practitioners for online consultations and prescription treatments.",
  keywords:
    "anti-aging treatment, weight loss, skin care, muscle support, online consultation, healthcare, medical prescriptions",
  authors: [{ name: "Revolved" }],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <NavBar />
      <div className="font-quinn mx-auto mt-0 w-full max-w-[2560px]">{children}</div>
      <Footer />
    </>
  );
}
