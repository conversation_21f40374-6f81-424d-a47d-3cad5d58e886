"use client";
import { useGetTreatmentBySlug } from "@/api/category-service";
import SuggestedProducts from "@/components/treatments/suggested-products";
import TreatmentHeader from "@/components/treatments/treatment-header";
import TreatmentIngredientsListSection from "@/components/treatments/treatment-ingredients-list-section";
import { TreatmentProductDetail } from "@/components/treatments/treatment-product-details";
import { CategoryDetail } from "@/data";
import { useParams } from "next/navigation";

const TreatmentDetailPage = () => {
  // const scrollContainerRef = useRef<HTMLDivElement>(null);

  // const scroll = (direction: "left" | "right") => {
  //   if (scrollContainerRef.current) {
  //     const container = scrollContainerRef.current;
  //     const scrollAmount = container.clientWidth;
  //     const scrollPosition =
  //       direction === "left"
  //         ? container.scrollLeft - scrollAmount
  //         : container.scrollLeft + scrollAmount;

  //     container.scrollTo({
  //       left: scrollPosition,
  //       behavior: "smooth",
  //     });
  //   }
  // };

  const { slug } = useParams();

  console.log("slug", slug);

  const { categoryDetail, categoryDetailLoading } = useGetTreatmentBySlug({
    slug: slug as string,
  });

  return (
    <div className="relative mt-16 flex w-full flex-col items-center gap-12 md:mt-20 md:gap-16 lg:gap-24">
      <TreatmentHeader category={categoryDetail} isCategoryDetailLoading={categoryDetailLoading} />
      <TreatmentProductDetail />
      <TreatmentIngredientsListSection />
      <section className="relative flex h-full w-full flex-col justify-center gap-4">
        <div className="justify-start self-stretch text-center">
          <span className="font-henju text-3xl font-normal text-black">Shop Our </span>
          <span className="font-henju text-3xl font-normal text-[#f2a472]">Wrinkle & Aging </span>
          <span className="font-henju text-3xl font-normal text-black">Essentials</span>
        </div>
        <SuggestedProducts products={CategoryDetail.suggestProducts} />
      </section>
    </div>
  );
};

export default TreatmentDetailPage;
