@import "tailwindcss";

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out forwards;
  }
}

body {
  @apply font-quinn bg-white text-black;
}

/* Font classes */
/* .font-univa-nova {
  font-family: var(--font-univa-nova);
} */

.font-henju {
  font-family: var(--font-henju);
}

.font-quinn {
  font-family: var(--font-quinn);
}

@theme {
  --color-primary: #85b5a5;
  --color-primary-hover: #6a9189;
  --color-secondary: #f2a472;
  --color-muted: hsl(210 40% 96.1%);

  --font-syne: var(--font-syne);
  --font-dm-sans: var(--font-dm-sans);
  --font-univa-nova: var(--font-univa-nova);
  --font-henju: var(--font-henju);
  --font-quinn: var(--font-quinn);
}

.gauge {
  background: conic-gradient(#f97316 0% 73%, /* 29.4 out of 40 -> 73% */ #fef2f2 73% 100%);
  border-radius: 100% 100% 0 0;
  clip-path: inset(0 0 50% 0);
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.text-md {
  @apply text-[1rem] leading-[1.2];
}

.text-2md {
  @apply text-[1rem] leading-[1.3];
}
