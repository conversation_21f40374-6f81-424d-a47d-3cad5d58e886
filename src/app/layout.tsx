import { ProtectedRoute } from "@/components/auth/protected-route";
import { ToastProvider } from "@/context/toast-context";
import type { Metadata } from "next";
import { dmSansFont, henjuFont, quinnFont } from "@/lib/fonts";
import "./globals.css";

export const metadata: Metadata = {
  title: "Revolved | Modern Healthcare Solutions",
  description:
    "Access personalized treatments for anti-aging, weight loss, skin care, and more. Connect with qualified healthcare practitioners for online consultations and prescription treatments.",
  keywords:
    "anti-aging treatment, weight loss, skin care, muscle support, online consultation, healthcare, medical prescriptions",
  authors: [{ name: "Revolved" }],
  // openGraph: {
  //   title: "Revolved - Your Trusted Healthcare Platform",
  //   description:
  //     "Access personalized treatments and connect with qualified healthcare practitioners for online consultations.",
  //   url: "https://revolved.mvp-apps.ae",
  //   siteName: "Revolved Health",
  //   images: [
  //     {
  //       url: "/images/og-image.jpg",
  //       width: 1200,
  //       height: 630,
  //       alt: "Revolved Health Platform",
  //     },
  //   ],
  //   locale: "en_US",
  //   type: "website",
  // },
  // twitter: {
  //   card: "summary_large_image",
  //   title: "Revolved - Modern Healthcare Solutions",
  //   description:
  //     "Personalized treatments and online consultations with qualified healthcare practitioners.",
  //   images: ["/images/twitter-image.jpg"],
  // },
  // viewport: "width=device-width, initial-scale=1",
  // robots: {
  //   index: true,
  //   follow: true,
  //   googleBot: {
  //     index: true,
  //     follow: true,
  //     "max-video-preview": -1,
  //     "max-image-preview": "large",
  //     "max-snippet": -1,
  //   },
  // },
  // themeColor: "#ffffff",
  // manifest: "/site.webmanifest",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${quinnFont.variable} ${dmSansFont.variable} ${henjuFont.variable}`}>
        <ToastProvider>
          <ProtectedRoute>{children}</ProtectedRoute>
        </ToastProvider>
      </body>
    </html>
  );
}
