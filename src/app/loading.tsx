"use client";

import { motion } from "framer-motion";

export default function Loading() {
  return (
    <div className="flex min-h-[80vh] items-center justify-center">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="flex flex-col items-center space-y-6"
      >
        {/* Logo Animation */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="relative h-16 w-16"
        >
          <svg
            viewBox="0 0 34 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="h-full w-full text-[#23a1b0]"
          >
            <path
              d="M10.1891 31.8836C10.1891 31.8836 5.21236 21.9261 5.24728 12.6389C5.28221 3.3518 9.69139 -0.456026 13.1751 2.9585C16.6588 6.37302 17.7676 12.1295 16.1611 18.2077C14.5546 24.2859 12.9044 26.9496 12.9044 26.9496C12.9044 26.9496 23.4341 27.1462 29.9038 21.2289C36.3735 15.3116 33.1168 7.30263 28.0266 6.88252C23.6698 6.52498 21.941 9.00096 21.941 9.00096C21.941 9.00096 27.8345 7.39202 28.6378 13.2736C29.441 19.1551 26.455 21.2736 21.976 23.4278C17.4969 25.582 15.5848 25.8948 15.5848 25.8948C15.5848 25.8948 19.9154 18.5652 19.9154 11.0747C19.9154 3.5842 14.9824 -0.965523 8.77462 0.169673C2.56685 1.30487 -0.410439 9.50152 0.0435759 15.6602C0.506321 21.8099 4.48768 26.8691 6.67044 29.1038C8.8532 31.3384 10.1891 31.8836 10.1891 31.8836Z"
              fill="currentColor"
            />
          </svg>
        </motion.div>

        {/* Loading Text */}
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-lg text-[#23a1b0]"
        >
          Loading...
        </motion.p>

        {/* Loading Bar */}
        <motion.div className="h-1 w-48 overflow-hidden rounded-full bg-[#e9e9e9]">
          <motion.div
            className="h-full bg-[#23a1b0]"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        </motion.div>
      </motion.div>
    </div>
  );
}
