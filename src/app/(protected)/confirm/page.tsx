"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import RevolvedLogo from "@/components/nav/nav-bar-logo";
import SuccessIcon from "@/components/success-icon";

const ConfirmPage = () => {
  const router = useRouter();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex min-h-screen flex-col items-center justify-center px-4 py-8 sm:py-16"
    >
      <Link
        href="/"
        className="h-12 w-28 overflow-hidden text-center md:absolute md:top-4 md:left-10 md:mb-2 md:h-[4rem] md:w-[8rem]"
      >
        <RevolvedLogo className="h-full w-full" />
      </Link>
      <div className="w-full max-w-lg text-center 2xl:max-w-[2560px]">
        {/* Success Icon */}
        <SuccessIcon />

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col items-center space-y-6"
        >
          <h1 className="font-henju text-2xl font-normal text-black sm:text-3xl">
            Booking Confirmed
          </h1>

          <p className="text-2md px-4 font-light text-black">
            Your booking has been confirmed! A Prescriber will be in touch with you and discuss your
            treatment plan.
          </p>

          <Button
            onClick={() => router.push("/profile?tab=my-treatments")}
            withArrow
            variant="primary"
            className="w-full max-w-sm min-w-36 py-2 pr-2 pl-5"
          >
            My Treatments
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ConfirmPage;
