"use client";
import DateSlotSelector from "@/components/date-slot-selector";
import Revolved<PERSON>ogo from "@/components/nav/nav-bar-logo";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

const BookingPage = () => {
  const searchParams = useSearchParams();
  const categoryId = searchParams.get("categoryId");

  return (
    <main className="flex min-h-screen flex-col items-center px-4 pt-6 pb-4 sm:px-6 sm:pt-12 md:px-8 md:pt-24 lg:px-12 lg:pt-36 xl:px-16">
      <Link href="/" className="overflow-hidden text-center md:absolute md:top-4 md:left-10">
        <RevolvedLogo className="h-20 w-36" />
      </Link>
      {/* Progress Bar */}
        <div className="hidden h-[3px] w-full max-w-2xl flex-1 rounded-full md:absolute md:top-12 lg:block lg:w-1/2 xl:w-2/5">
            <div className=" mb-6 h-[3px] w-full rounded-full bg-[#E9E9E9]">
            <div
              className="bg-primary absolute -top-[1.5px] left-0 h-[5px] rounded-full transition-all duration-300"
              style={{
                width: `${100}%`,
              }}
            />
            </div>
          </div>
      {/* Content Section */}
      <div className="w-full max-w-2xl flex-1 lg:w-2/5">
        
        <div className="mb-6">
          <h1 className="font-henju mb-3 text-2xl font-normal sm:mb-4 sm:text-3xl md:text-4xl">
            Pick Date & Time
          </h1>

          <p className="text-md md:text-2md text-neutral-600">
            Pick a date and time for your consultation
          </p>
          <div className="py-4">
            <DateSlotSelector
              categoryId= {categoryId || ""}
              disabledDates={[
                // Example of disabled dates in YYYY-MM-DD format
                // Current month
                // `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}-15`,
                // `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}-20`,
                // `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}-25`,
              ]}
              disabledTimes={[
                // Example of disabled time slots
                // "10:30 AM",
                // "11:30 AM",
                // "2:00 PM",
                // "4:30 PM",
              ]}
              buttonText="Book Your Treatment"
              redirectPath="/confirm"
            />
          </div>
        </div>
      </div>
    </main>
  );
};

export default BookingPage;
