"use client";

import QuestionnaireLayout from "@/components/questionnaire/questionnaire-layout";
import { QuestionnaireProvider } from "@/context/questionnaire-context";
import { useSearchParams } from "next/navigation";

const QuestionnairePage = () => {
  const searchParams = useSearchParams();
  const categoryId = searchParams.get("category") || "67f384c4b5478304e0642e31";

  return (
    <QuestionnaireProvider categoryId={categoryId}>
      <QuestionnaireLayout />
    </QuestionnaireProvider>
  );
};

export default QuestionnairePage;
