"use client";

import MyOrdersSection from "@/components/profile/my-orders-section";
import MyTreatmentsSection from "@/components/profile/my-treatments-section";
import ProfileSection from "@/components/profile/profile-section";
import TabGroup from "@/components/ui/tab-group";
import { useSearchParams } from "next/navigation";
import { useCallback, useMemo, useState } from "react";

type TabIdTypes = "profile" | "my-treatments" | "my-orders";

export default function ProfilePage() {

    const searchParams = useSearchParams();
    const [activeTab, setActiveTab] = useState<TabIdTypes>( searchParams.get("tab") as TabIdTypes || "profile");
  
    const tabs = [
      { id: "profile", label: "Profile" },
      { id: "my-treatments", label: "My Treatments" },
      { id: "my-orders", label: "My Orders" },
    ];
  
    const handleTabChange = useCallback((tabId: TabIdTypes) => {
      
      setActiveTab(tabId);
    }, []);
  
    return (
    <div className="mt-16 min-h-screen w-full bg-white px-4 py-4 sm:px-6 md:mt-24 md:px-8">
     <div className="w-full max-w-[400px]">
               {useMemo(
                 () => (
                   <TabGroup tabs={tabs} activeTab={activeTab} onTabChange={handleTabChange} tabClassName="text-2md whitespace-nowrap bg-white p-0 hover:bg-white hover:text-[#85B5A5]" activeTabClassName="text-[#85B5A5] scale-105" inactiveTabClassName="bg-white text-[#CECECE]" />
                 ),
                 [tabs, activeTab, handleTabChange]
               )}
      </div>

      <div className="mt-8">
           {activeTab === "profile" && <ProfileSection />}
          {activeTab === "my-treatments" && <MyTreatmentsSection />}
          {activeTab === "my-orders" && <MyOrdersSection />}
      </div>
    </div>
  );
}
