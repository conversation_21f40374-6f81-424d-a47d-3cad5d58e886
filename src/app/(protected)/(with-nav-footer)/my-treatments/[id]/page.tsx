"use client";
import { useGetBookingDetail } from "@/api/booking-service";
import Bag from "@/components/bag/bag";
import BagIcon from "@/components/icons/bag-icon";
import ProductDetailsDialog from "@/components/my-treatments/product-details-dialog";
import Badge from "@/components/ui/badge";
import { useToast } from "@/context/toast-context";
import { formatBookingDateTime, getBookingStatusColor } from "@/lib/utils";
import { useBagStore } from "@/store/bag-store";
import { Calendar, CheckIcon, Clock, Minus, Plus } from "lucide-react";
import Image from "next/image";
import { useParams } from "next/navigation";
import { useState } from "react";

// Types
export interface TreatmentItem {
  id: string;
  name: string;
  concentration: string;
  category: string;
  instructions: string;
  price: number;
  isAdded: boolean;
  imageUrl?: string;
  details?: Array<{
    title: string;
    description: string;
  }>;
  moreDetails?: Array<{
    title: string;
    description: string;
  }>;
  compounds?: string[];
}

const treatments: TreatmentItem[] = [
  {
    id: "t1",
    name: "GHRP6 Cream + CJC1295 30mL",
    concentration: "400mcg/mL + 600mcg/mL",
    compounds: ["Pentapeptide, 0.3 mg", "Ghrelin Mimetic, 0.1 mg", "CO2", "Garbage"],
    category: "Anti-aging",
    instructions: "Once daily at night on an empty stomach for 5 days a week.",
    price: 304,
    isAdded: false,
    details: [
      {
        title: "How It Works",
        description:
          "CJC 1295 is a peptide that helps stimulate your body’s natural production of growth hormone. As we age, our growth hormone levels naturally decline, and that can affect things like muscle mass, fat metabolism, and even skin elasticity. CJC 1295 can help counteract this by encouraging your pituitary gland to release more growth hormone, which supports muscle growth, helps with fat breakdown, and boosts collagen production for healthier, younger-looking skin. It can also improve your sleep, especially deep sleep, which is crucial for muscle recovery and overall restoration.Now, when you combine CJC 1295 with a growth hormone-releasing peptide like Ipamorelin, you get even better results. This combination can significantly enhance the production of growth hormone, so you could see even more improvements in muscle development, skin tone, and overall vitality.",
      },
      {
        title: "Dosage and Administration",
        description: "test",
      },
    ],
    moreDetails: [
      {
        title: "Pentapeptide",
        description:
          "It stimulates collagen production and improves skin elasticity, in turn, reducing wrinkles and giving the skin a youthful glow. It is also great for moisturizing and healing the skin.",
      },
      {
        title: "Ghrelin Mimetic",
        description:
          "Stimulates growth hormone release, influences insulin regulation, and supports cardiovascular health.",
      },
    ],
  },
  {
    id: "t2",
    name: "Ageless Radiance",
    concentration: "",
    category: "Hair loss",
    instructions: "Once daily at night on an empty stomach for 5 days a week",
    price: 423,
    isAdded: false,
  },
  {
    id: "t3",
    name: "Ageless Dummy",
    concentration: "",
    category: "Hair loss",
    instructions:
      "Once daily at night on an empty stomach for 5 days a week, don't eat and drink, this is only for testing.",
    price: 12,
    isAdded: false,
  },
  {
    id: "t4",
    name: "Ageless Radiance Clone",
    concentration: "",
    category: "Hair loss",
    instructions: "Once daily at night on an empty stomach for 5 days a week",
    price: 12,
    isAdded: false,
  },
];

const TreatmentDetailPage = () => {
  const { id } = useParams();
  const { showToast } = useToast();
  const { bookingDetail, bookingDetailLoading, bookingDetailError } = useGetBookingDetail({
    bookingId: id as string,
  });

  console.log("bookingDetail", bookingDetail);

  if (bookingDetailLoading) {
    return <TreatmentDetailSkeleton />;
  }

  const { dateString, timeString } = formatBookingDateTime(bookingDetail?.bookingTime);

  return (
    <main className="mt-16 flex min-h-screen w-full flex-col gap-4 bg-white px-4 py-4 sm:px-6 sm:py-6 md:mt-24 md:gap-8 lg:flex-row lg:px-16 lg:py-8">
      <div className="mx-auto max-w-[1728px] flex-1">
        {/* Header Section */}
        <header className="relative">
          <div className="flex items-center gap-2">
            <h1 className="font-henju text-2xl sm:text-3xl">
              {bookingDetail?.category?.name || "Unknonw Treatment"}
            </h1>
            <Badge
              variant={getBookingStatusColor(bookingDetail?.status)}
              withDot
              size="sm"
              className="capitalize"
            >
              {bookingDetail?.status}
            </Badge>
          </div>
          <p className="text-md font-light text-neutral-600">Online consultation</p>
        </header>

        {/* Doctor Info */}
        <section className="flex items-center gap-4 py-4">
          <div className="flex size-10 items-center justify-center rounded-full bg-[#e9e9e9]">
            <Image
              src={bookingDetail?.doctor?.image || "/icons/doctor-avatar-placeholder.svg"}
              alt="Doctor"
              width={30}
              height={30}
              className="size-10 rounded-full object-cover"
            />
          </div>
          <div>
            <p className="text-2md text-black">{bookingDetail?.doctor?.name || "Unknown Doctor"}</p>
            <p className="text-md text-neutral-600">
              {bookingDetail?.doctorProfile?.education || "Unknown"}
            </p>
          </div>
        </section>

        {/* Appointment Details */}
        <section className="flex flex-wrap gap-3 p-2 sm:gap-4 md:gap-8">
          <div className="flex items-center gap-1">
            <div className="flex size-4 items-center justify-center">
              <Calendar className="size-3 text-[#535353]" />
            </div>
            <span className="text-md font-light text-black">{dateString}</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="flex size-4 items-center justify-center">
              <Clock className="size-3 text-[#535353]" />
            </div>
            <span className="text-md font-light text-black">{timeString}</span>
          </div>
        </section>
        <div className="justify-start px-2">
          <span className="text-md font-light text-black">
            You didn&apos;t received a call? Please{" "}
          </span>
          <span className="text-md font-light text-[#23a1b0]">contact us</span>
          <span className="text-md font-light text-black">.</span>
        </div>
        {/* Treatment Plan Section */}
        <section>
          <TreatmentPlanSection />
        </section>
      </div>
      <div className="h-full rounded-3xl bg-white pb-4 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)] md:min-w-[463px]">
        <Bag />
      </div>
    </main>
  );
};

export default TreatmentDetailPage;

const TreatmentPlanSection = () => {
  const { items, addItem, removeItem, isDeletingItem } = useBagStore();
  const { showToast } = useToast();
  const [selectedTreatment, setSelectedTreatment] = useState<TreatmentItem | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  // Check if a treatment is in the bag
  const isTreatmentInBag = (treatmentId: string) => {
    return items.some((item) => item.productId && (item.productId as any)._id === treatmentId);
  };

  // Add a treatment to the bag
  const handleAddTreatment = (treatment: TreatmentItem) => {
    const bagItem = {
      productId: {
        _id: treatment.id,
        name: treatment.name,
        categories: [{ _id: "1", name: treatment.category }],
        price: treatment.price,
        // Add other required fields for IProductSingleItem
        main_image: treatment.imageUrl || "",
        images: [],
        isActive: true,
        isPublic: true,
        minOrderQuantity: 1,
        maxOrderQuantity: 10,
        tags: [],
        stock: 100,
        rating: 5,
        slug: treatment.id,
        productDetails: (treatment.details || []).map((detail) => ({
          _id: Math.random().toString(36).substring(2, 9),
          title: detail.title,
          description: detail.description,
        })),
        clinicalDetails: [],
        compounds: [],
        deletedAt: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        __v: 0,
        description: treatment.instructions,
      },
      quantity: 1,
      isPrescription: false,
    };

    addItem(bagItem);
    showToast(`${treatment.name} added to your bag`, "success");
  };

  // Remove a treatment from the bag
  const handleRemoveTreatment = async (treatment: TreatmentItem) => {
    // Find the item in the bag with this treatment ID
    const bagItem = items.find(
      (item) => item.productId && (item.productId as any)._id === treatment.id
    );

    if (bagItem && bagItem._id) {
      try {
        await removeItem(bagItem._id);
        showToast(`${treatment.name} removed from your bag`, "info");
      } catch (error) {
        showToast(`Failed to remove ${treatment.name} from your bag`, "error");
      }
    }
  };

  // Add all treatments to the bag
  const handleAddAllTreatments = () => {
    treatments.forEach((treatment) => {
      if (!isTreatmentInBag(treatment.id)) {
        const bagItem = {
          productId: {
            _id: treatment.id,
            name: treatment.name,
            categories: [{ _id: "1", name: treatment.category }],
            price: treatment.price,
            // Add other required fields for IProductSingleItem
            main_image: treatment.imageUrl || "",
            images: [],
            isActive: true,
            isPublic: true,
            minOrderQuantity: 1,
            maxOrderQuantity: 10,
            tags: [],
            stock: 100,
            rating: 5,
            slug: treatment.id,
            productDetails: (treatment.details || []).map((detail) => ({
              _id: Math.random().toString(36).substring(2, 9),
              title: detail.title,
              description: detail.description,
            })),
            clinicalDetails: [],
            compounds: [],
            deletedAt: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            __v: 0,
            description: treatment.instructions,
          },
          quantity: 1,
          isPrescription: false,
        };
        addItem(bagItem);
      }
    });
    showToast("All treatments added to your bag", "success");
  };

  // Open product details dialog
  const handleOpenProductDetails = (treatment: TreatmentItem) => {
    setSelectedTreatment(treatment);
    setIsDetailsDialogOpen(true);
  };

  // Close product details dialog
  const handleCloseProductDetails = () => {
    setIsDetailsDialogOpen(false);
  };

  return (
    <div className="px-2 py-4">
      {/* Product Details Dialog */}
      <ProductDetailsDialog
        isOpen={isDetailsDialogOpen}
        onClose={handleCloseProductDetails}
        treatment={selectedTreatment}
      />

      <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center sm:gap-0">
        <div className="flex flex-col gap-0">
          <p className="font-henju text-xl">Treatment Plan</p>
          <p className="py-0">
            <span className="py- text-md font-light text-black">Treatment Expiry after </span>
            <span className="text-md font-light text-[#23a1b0]">60</span>
            <span className="text-md font-light text-black"> days</span>
          </p>
        </div>
        <button
          className="flex touch-manipulation items-center self-start rounded-full bg-[#E6FAF7] p-1 pr-4 pl-2 transition-all duration-200 hover:scale-105 hover:bg-[#d0f5ef] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#E6FAF7]"
          onClick={handleAddAllTreatments}
        >
          <Plus className="size-5 stroke-[#40BFAA] stroke-1 disabled:stroke-[#999999]" />
          <span className="text-md font-light text-[#40BFAA]">Add all</span>
        </button>
      </div>

      {/* Desktop Table View (hidden on mobile) */}
      <div className="mt-4 hidden w-full space-y-4 sm:block">
        <table className="w-full">
          <thead className="w-full border-b border-[#f4feff] bg-white">
            <tr>
              <th className="text-md px-4 py-2 text-left font-semibold text-neutral-600">
                Treatment
              </th>
              <th className="text-md px-4 py-2 text-left font-semibold text-neutral-600">
                Dosing Instructions
              </th>
              <th className="text-md px-4 py-2 text-center font-semibold text-neutral-600">
                Price
              </th>
              <th className="text-md px-4 py-2 text-center font-semibold text-neutral-600">
                Action
              </th>
            </tr>
          </thead>
          <tbody>
            {treatments.map((treatment) => {
              const isInBag = isTreatmentInBag(treatment.id);
              return (
                <tr key={treatment.id} className="border-b border-[#f4feff] bg-white">
                  <td className="px-4 py-4">
                    <div className="flex items-center gap-3">
                      {isInBag && (
                        <div className="flex size-5 items-center justify-center rounded-full bg-[#23a1b0]">
                          <CheckIcon className="size-3 stroke-[#e9e9e9]" />
                        </div>
                      )}
                      <div>
                        <p className="text-2md font-light">{treatment.name}</p>
                        <Badge variant="pill" size="sm">
                          {treatment.category}
                        </Badge>
                      </div>
                    </div>
                  </td>
                  <td className="text-2md px-4 py-4 font-light">
                    <div className="flex gap-2">
                      <p className="text-md w-60 font-light">{treatment.instructions}</p>
                      <button
                        className="h-6 min-w-10 cursor-pointer touch-manipulation truncate rounded-full border border-[#23a1b0] px-2 py-1 text-center text-[9px] text-[#23a1b0] transition-all duration-200 hover:scale-105 active:scale-95"
                        onClick={() => handleOpenProductDetails(treatment)}
                      >
                        Product Details
                      </button>
                    </div>
                  </td>
                  <td className="text-2md px-4 py-4 text-center font-light">${treatment.price}</td>
                  <td className="text-2md px-4 py-4 text-center font-light">
                    {isInBag ? (
                      <button
                        className="touch-manipulation rounded-full bg-[#FFE2E2] p-1 transition-all duration-200 hover:scale-105 hover:bg-[#ffcaca] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#EDEDED]"
                        onClick={() => handleRemoveTreatment(treatment)}
                        disabled={isDeletingItem}
                      >
                        {isDeletingItem ? (
                          <span className="size-4 animate-spin rounded-full border-2 border-[#873A3A] border-t-transparent" />
                        ) : (
                          <Minus className="size-4 stroke-[#873A3A] stroke-1 disabled:stroke-[#999999]" />
                        )}
                      </button>
                    ) : (
                      <button
                        className="touch-manipulation rounded-full bg-[#E6FAF7] p-1 transition-all duration-200 hover:scale-105 hover:bg-[#d0f5ef] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#E6FAF7]"
                        onClick={() => handleAddTreatment(treatment)}
                      >
                        <Plus className="size-4 stroke-[#40BFAA] stroke-1 disabled:stroke-[#999999]" />
                      </button>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
        <EveryDayEssentialsProductSection />
      </div>

      {/* Mobile Card View (visible only on mobile) */}
      <div className="mt-4 w-full space-y-4 sm:hidden">
        {treatments.map((treatment) => {
          const isInBag = isTreatmentInBag(treatment.id);
          return (
            <div
              key={treatment.id}
              className="rounded-lg border border-[#f4feff] bg-white p-4 shadow-sm"
            >
              <div className="mb-3 flex items-start justify-between">
                <div className="flex items-start gap-2">
                  {isInBag && (
                    <div className="mt-1 flex size-5 flex-shrink-0 items-center justify-center rounded-full bg-[#23a1b0]">
                      <CheckIcon className="size-3 stroke-[#e9e9e9]" />
                    </div>
                  )}
                  <div>
                    <p className="text-2md font-medium">{treatment.name}</p>
                    <Badge variant="pill" size="sm" className="mt-1">
                      {treatment.category}
                    </Badge>
                  </div>
                </div>
                <div>
                  {isInBag ? (
                    <button
                      className="touch-manipulation rounded-full bg-[#FFE2E2] p-2 transition-all duration-200 hover:scale-105 hover:bg-[#ffcaca] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#EDEDED]"
                      onClick={() => handleRemoveTreatment(treatment)}
                      disabled={isDeletingItem}
                    >
                      {isDeletingItem ? (
                        <span className="size-5 animate-spin rounded-full border-2 border-[#873A3A] border-t-transparent" />
                      ) : (
                        <Minus className="size-5 stroke-[#873A3A] stroke-1 disabled:stroke-[#999999]" />
                      )}
                    </button>
                  ) : (
                    <button
                      className="touch-manipulation rounded-full bg-[#E6FAF7] p-2 transition-all duration-200 hover:scale-105 hover:bg-[#d0f5ef] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#E6FAF7]"
                      onClick={() => handleAddTreatment(treatment)}
                    >
                      <Plus className="size-5 stroke-[#40BFAA] stroke-1 disabled:stroke-[#999999]" />
                    </button>
                  )}
                </div>
              </div>

              <div className="mb-3">
                <p className="text-md mb-1 text-neutral-600">Dosing Instructions:</p>
                <p className="text-md font-light">{treatment.instructions}</p>
              </div>

              <div className="flex items-center justify-between">
                <p className="text-2md font-medium">${treatment.price}</p>
                <button
                  className="text-md h-8 min-w-10 cursor-pointer touch-manipulation rounded-full border border-[#23a1b0] px-3 py-1 text-center text-[#23a1b0] transition-all duration-200 hover:scale-105 active:scale-95"
                  onClick={() => handleOpenProductDetails(treatment)}
                >
                  Product Details
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const EveryDayEssentialsProductSection = () => {
  return (
    <div className="mx-auto mt-8 max-w-[1728px] flex-1">
      <h1 className="font-henju mb-6 text-xl">Everyday Essentials</h1>
      <div className="grid w-full max-w-7xl grid-cols-1 place-items-center gap-8 sm:grid-cols-2 lg:grid-cols-4 2xl:max-w-[1728px] 2xl:grid-cols-4">
        {[1, 2, 3, 4, 5, 6].map((_, index) => (
          <MiniProductCard product={index} key={index} />
        ))}
      </div>
    </div>
  );
};

const MiniProductCard = ({ product }: any) => {
  return (
    <div className="relative h-60 w-48 rounded-[40px] bg-neutral-50 p-4 transition-shadow hover:shadow-md">
      <div className="relative h-[112px] w-full rounded-[20px]">
        <Image
          src={`/images/products/product${(product + 1) % 3 === 0 ? 1 : (product + 1) % 3}.png`}
          alt={`Product ${product}`}
          fill
          className="object-contain"
        />
      </div>
      <div className="mt-2 flex flex-col items-center justify-center gap-0.5">
        <h3 className="font-henju text-xl">Glow Booster</h3>
        <p className="text-md text-center text-neutral-600">
          Clinically-backed formulas for radiant skin.
        </p>
        <p className="text-2md font-medium text-black">$100</p>
      </div>
      <button className="absolute top-1/2 right-4 bottom-1/2 flex h-7 w-7 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-white">
        <BagIcon color="#000000" />
      </button>
    </div>
  );
};

// Skeleton Components
const TreatmentDetailSkeleton = () => {
  return (
    <main className="mt-16 flex min-h-screen w-full flex-col gap-4 bg-white px-4 py-4 sm:px-6 sm:py-6 md:mt-24 md:gap-8 lg:flex-row lg:px-16 lg:py-8">
      <div className="mx-auto max-w-[1728px] flex-1">
        {/* Header Section Skeleton */}
        <header className="relative">
          <div className="flex items-center gap-2">
            <div className="h-8 w-64 animate-pulse rounded-full bg-neutral-200 sm:h-9 sm:w-80" />
            <div className="h-6 w-32 animate-pulse rounded-full bg-neutral-200" />
          </div>
          <div className="mt-2 h-5 w-40 animate-pulse rounded-full bg-neutral-200" />
        </header>

        {/* Doctor Info Skeleton */}
        <section className="flex items-center gap-4 py-4">
          <div className="size-10 animate-pulse rounded-full bg-neutral-200" />
          <div>
            <div className="h-6 w-40 animate-pulse rounded-full bg-neutral-200" />
            <div className="mt-1 h-4 w-60 animate-pulse rounded-full bg-neutral-200" />
          </div>
        </section>

        {/* Appointment Details Skeleton */}
        <section className="flex flex-wrap gap-3 p-2 sm:gap-4 md:gap-8">
          <div className="flex items-center gap-1">
            <div className="size-4 animate-pulse rounded-full bg-neutral-200" />
            <div className="h-4 w-32 animate-pulse rounded-full bg-neutral-200" />
          </div>
          <div className="flex items-center gap-1">
            <div className="size-4 animate-pulse rounded-full bg-neutral-200" />
            <div className="h-4 w-20 animate-pulse rounded-full bg-neutral-200" />
          </div>
        </section>

        <div className="justify-start px-2">
          <div className="h-4 w-80 animate-pulse rounded-full bg-neutral-200" />
        </div>

        {/* Treatment Plan Section Skeleton */}
        <section>
          <TreatmentPlanSkeleton />
        </section>
      </div>

      {/* Bag Skeleton */}
      <div className="h-full rounded-3xl bg-white pb-4 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)] md:min-w-[463px]">
        <BagSkeleton />
      </div>
    </main>
  );
};

const TreatmentPlanSkeleton = () => {
  return (
    <div className="px-2 py-4">
      <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center sm:gap-0">
        <div className="flex flex-col gap-0">
          <div className="h-6 w-40 animate-pulse rounded-full bg-neutral-200" />
          <div className="mt-2 h-4 w-60 animate-pulse rounded-full bg-neutral-200" />
        </div>
        <div className="h-8 w-20 animate-pulse rounded-full bg-neutral-200" />
      </div>

      {/* Desktop Table Skeleton (hidden on mobile) */}
      <div className="mt-4 hidden w-full space-y-4 sm:block">
        <table className="w-full">
          <thead className="w-full border-b border-[#f4feff] bg-white">
            <tr>
              <th className="px-4 py-2 text-left">
                <div className="h-4 w-20 animate-pulse rounded-full bg-neutral-200" />
              </th>
              <th className="px-4 py-2 text-left">
                <div className="h-4 w-32 animate-pulse rounded-full bg-neutral-200" />
              </th>
              <th className="px-4 py-2 text-center">
                <div className="mx-auto h-4 w-12 animate-pulse rounded-full bg-neutral-200" />
              </th>
              <th className="px-4 py-2 text-center">
                <div className="mx-auto h-4 w-16 animate-pulse rounded-full bg-neutral-200" />
              </th>
            </tr>
          </thead>
          <tbody>
            {[1, 2, 3, 4].map((index) => (
              <tr key={index} className="border-b border-[#f4feff] bg-white">
                <td className="px-4 py-4">
                  <div className="flex items-center gap-3">
                    <div className="size-5 animate-pulse rounded-full bg-neutral-200" />
                    <div>
                      <div className="h-5 w-48 animate-pulse rounded-full bg-neutral-200" />
                      <div className="mt-1 h-4 w-20 animate-pulse rounded-full bg-neutral-200" />
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="flex gap-2">
                    <div className="h-4 w-60 animate-pulse rounded-full bg-neutral-200" />
                    <div className="h-6 w-24 animate-pulse rounded-full bg-neutral-200" />
                  </div>
                </td>
                <td className="px-4 py-4 text-center">
                  <div className="mx-auto h-5 w-12 animate-pulse rounded-full bg-neutral-200" />
                </td>
                <td className="px-4 py-4 text-center">
                  <div className="mx-auto size-8 animate-pulse rounded-full bg-neutral-200" />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <EveryDayEssentialsSkeleton />
      </div>

      {/* Mobile Card Skeleton (visible only on mobile) */}
      <div className="mt-4 w-full space-y-4 sm:hidden">
        {[1, 2, 3, 4].map((index) => (
          <div key={index} className="rounded-lg border border-[#f4feff] bg-white p-4 shadow-sm">
            <div className="mb-3 flex items-start justify-between">
              <div className="flex items-start gap-2">
                <div className="mt-1 size-5 animate-pulse rounded-full bg-neutral-200" />
                <div>
                  <div className="h-5 w-40 animate-pulse rounded-full bg-neutral-200" />
                  <div className="mt-1 h-4 w-16 animate-pulse rounded-full bg-neutral-200" />
                </div>
              </div>
              <div className="size-9 animate-pulse rounded-full bg-neutral-200" />
            </div>

            <div className="mb-3">
              <div className="mb-1 h-4 w-32 animate-pulse rounded-full bg-neutral-200" />
              <div className="h-4 w-full animate-pulse rounded-full bg-neutral-200" />
            </div>

            <div className="flex items-center justify-between">
              <div className="h-5 w-12 animate-pulse rounded-full bg-neutral-200" />
              <div className="h-8 w-24 animate-pulse rounded-full bg-neutral-200" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const EveryDayEssentialsSkeleton = () => {
  return (
    <div className="mx-auto mt-8 max-w-[1728px] flex-1">
      <div className="mb-6 h-6 w-40 animate-pulse rounded-full bg-neutral-200" />
      <div className="grid w-full max-w-7xl grid-cols-1 place-items-center gap-8 sm:grid-cols-2 lg:grid-cols-4 2xl:max-w-[1728px] 2xl:grid-cols-4">
        {[1, 2, 3, 4, 5, 6].map((index) => (
          <MiniProductCardSkeleton key={index} />
        ))}
      </div>
    </div>
  );
};

const MiniProductCardSkeleton = () => {
  return (
    <div className="relative h-60 w-48 rounded-[40px] bg-neutral-50 p-4">
      <div className="relative h-[112px] w-full rounded-[20px]">
        <div className="h-full w-full animate-pulse rounded-[20px] bg-neutral-200" />
      </div>
      <div className="mt-2 flex flex-col items-center justify-center gap-0.5">
        <div className="h-6 w-32 animate-pulse rounded-full bg-neutral-200" />
        <div className="h-4 w-40 animate-pulse rounded-full bg-neutral-200" />
        <div className="h-5 w-16 animate-pulse rounded-full bg-neutral-200" />
      </div>
      <div className="absolute top-1/2 right-4 bottom-1/2 size-7 -translate-y-1/2 transform animate-pulse rounded-full bg-neutral-200" />
    </div>
  );
};

const BagSkeleton = () => {
  return (
    <div className="h-full p-6">
      {/* Bag Header */}
      <div className="mb-6 flex items-center gap-2">
        <div className="size-6 animate-pulse rounded-full bg-neutral-200" />
        <div className="h-6 w-20 animate-pulse rounded-full bg-neutral-200" />
      </div>

      {/* Bag Items */}
      <div className="space-y-4">
        {[1, 2, 3].map((index) => (
          <div key={index} className="flex items-center gap-4 rounded-lg p-4">
            <div className="size-16 animate-pulse rounded-lg bg-neutral-200" />
            <div className="flex-1">
              <div className="h-5 w-32 animate-pulse rounded-full bg-neutral-200" />
              <div className="mt-1 h-4 w-24 animate-pulse rounded-full bg-neutral-200" />
              <div className="mt-2 h-4 w-16 animate-pulse rounded-full bg-neutral-200" />
            </div>
            <div className="flex flex-col items-end gap-2">
              <div className="h-5 w-12 animate-pulse rounded-full bg-neutral-200" />
              <div className="flex items-center gap-2">
                <div className="size-6 animate-pulse rounded-full bg-neutral-200" />
                <div className="h-4 w-6 animate-pulse rounded-full bg-neutral-200" />
                <div className="size-6 animate-pulse rounded-full bg-neutral-200" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Bag Summary */}
      <div className="mt-6 space-y-3 border-t pt-4">
        <div className="flex justify-between">
          <div className="h-4 w-16 animate-pulse rounded-full bg-neutral-200" />
          <div className="h-4 w-12 animate-pulse rounded-full bg-neutral-200" />
        </div>
        <div className="flex justify-between">
          <div className="h-4 w-20 animate-pulse rounded-full bg-neutral-200" />
          <div className="h-4 w-12 animate-pulse rounded-full bg-neutral-200" />
        </div>
        <div className="flex justify-between border-t pt-2">
          <div className="h-5 w-12 animate-pulse rounded-full bg-neutral-200" />
          <div className="h-5 w-16 animate-pulse rounded-full bg-neutral-200" />
        </div>
      </div>

      {/* Checkout Button */}
      <div className="mt-6 h-12 w-full animate-pulse rounded-full bg-neutral-200" />
    </div>
  );
};
