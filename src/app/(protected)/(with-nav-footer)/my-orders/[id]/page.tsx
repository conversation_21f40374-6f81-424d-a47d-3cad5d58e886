"use client";

import Image from "next/image";
import Badge from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useToast } from "@/context/toast-context";
import TrackingCard from "@/components/my-orders/tracking-card";
import { notFound, useParams } from "next/navigation";
import { useGetOrderDetail } from "@/api/order-service";
import { formatBookingDateTime } from "@/lib/utils";
import { Copy } from "lucide-react";
import Link from "next/link";
import OrderProgressBar from "@/components/my-orders/order-progress-bar";

const OrderDetail = {
  id: "9292",
  date: "2025-03-10T14:34:00",
  status: "shipped",
  amount: 569.0,
  doctor: {
    name: "Dr. <PERSON>",
    title: "<PERSON><PERSON>, is a specialist in anti-aging medicine",
    image: "/icons/doctor-avatar-placeholder.svg",
  },
  prescriptionToken: "2DRPHCKTTGQKH4RMW7",
  prescriptionDate: "2024-09-11",
  prescriptionImage: "/images/dhl.jpg",
  treatments: [
    {
      name: "Domperidone Tablets 10mg",
      quantity: 50,
      instructions: "Once daily at night on an empty stomach for 5 days a week",
      price: 474,
      category: "Hair loss",
    },
    {
      name: "Epitalon 10mg/mL",
      quantity: 1,
      instructions: "Once daily at night on an empty stomach for 5 days a week",
      price: 237,
      category: "Anti-aging",
    },
  ],
};

const MyOrderDetailPage = () => {
  const { id } = useParams();
  const { showToast } = useToast();

  const { orderDetails, orderDetailsLoading, orderDetailsError } = useGetOrderDetail({
    orderId: id as string,
  });

  if (orderDetailsError) {
    return <div>Error loading order</div>;
  }

  if (!id) {
    return notFound();
  }

  console.log("orderDetails", orderDetails);
  const { dateString, timeString } = formatBookingDateTime(orderDetails?.createdAt);

  const handleTrackClick = () => {
    // In a real application, this would redirect to the shipping company's tracking page
    window.open("https://www.dhl.com/global-en/home/<USER>", "_blank");
  };

  return (
    <main className="mt-16 flex min-h-screen w-full flex-col gap-4 bg-white px-4 py-4 sm:px-6 sm:py-6 md:mt-24 md:gap-8 lg:flex-row lg:px-16 lg:py-8">
      <div className="mx-auto w-full max-w-[1728px] flex-1">
        {/* Header Section */}
        <header className="relative">
          <div className="mb-2 flex flex-col gap-2 sm:mb-0 sm:flex-row sm:items-center">
            <h1 className="font-henju text-xl sm:text-2xl md:text-3xl">
              Order ID#{orderDetails?.orderReference}
            </h1>
          </div>
          <p className="text-md font-light text-neutral-600">{`${dateString} ${timeString}`}</p>
        </header>

        {/* Doctor Info */}
        <section className="flex items-center gap-3 py-4 sm:gap-4">
          <div className="flex size-8 items-center justify-center rounded-full bg-[#e9e9e9] sm:size-10">
            <Image
              src={OrderDetail.doctor.image}
              alt={OrderDetail.doctor.name}
              width={30}
              height={30}
              className="size-5 rounded-full sm:size-6"
            />
          </div>
          <div>
            <p className="text-md sm:text-2md text-black">{OrderDetail.doctor.name}</p>
            <p className="sm:text-md text-[10px] text-neutral-600">{OrderDetail.doctor.title}</p>
          </div>
        </section>

        {/* Shipping Address Section */}
        <div className="inline-flex w-[325px] flex-col items-start justify-start gap-[3px] rounded-[10px] py-2.5 outline-offset-[-1px]">
          <div className="inline-flex items-start justify-start gap-[3px] self-stretch">
            <div className="justify-start text-[10px] font-normal text-black">Shipping address</div>
          </div>
          <div className="w-[264px] justify-start text-base font-normal text-black capitalize">
            {orderDetails?.deliveryAddress?.street}, {orderDetails?.deliveryAddress?.city},{" "}
            {orderDetails?.deliveryAddress?.state}
          </div>
        </div>

        <div className="flex items-center gap-10">
          <div className="flex flex-col items-start justify-start gap-[3px]">
            <div className="h-3 w-16 justify-center text-[10px] leading-normal font-normal text-black">
              Delivered by
            </div>

            <Image
              className="h-4 w-16 rounded-[5px]"
              src={"/images/dhl.png"}
              alt="Delivery Company"
              width={156}
              height={35}
            />
          </div>

          <div className="flex flex-col items-start justify-start gap-[3px]">
            <div className="h-3 w-16 justify-center text-[10px] leading-normal font-normal text-black">
              Tracking #
            </div>

            <div className="flex items-center gap-2">
              <p>{orderDetails?.fulfillment?._id || "456865"}</p>
              <button
                onClick={() => {
                  navigator.clipboard.writeText("456865");
                  showToast("Tracking number copied to clipboard", "success");
                }}
                className="rounded-full bg-white p-1"
              >
                <Copy className="hover:stroke-priamry size-4 stroke-2" />
              </button>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-start gap-1 py-2">
          <span className="text-base font-normal text-black">
            To view more details on the status of your order, please click{" "}
          </span>
          <button
            className="text-base font-normal text-[#85b5a5] underline"
            onClick={handleTrackClick}
          >
            here
          </button>
        </div>

        <div className="max-w-3xl">
          <OrderProgressBar />
        </div>

        {/* <div className="flex justify-start">
          <Button
            variant="primary"
            withArrow
            className="text-md sm:text-2md min-w-20 truncate py-1.5 pr-2 pl-3 sm:min-w-24 sm:py-2 lg:min-w-32 lg:pr-2 lg:pl-4"
          >
            View invoice
          </Button>
        </div> */}

        {/* Treatment Plan Section */}
        <section className="mt-6 w-full max-w-3xl sm:mt-8">
          <div className="px-0 py-3 sm:px-2 sm:py-4">
            <div className="flex flex-col gap-0">
              <p className="font-henju text-lg sm:text-xl">Treatment Plan</p>
            </div>

            {/* Desktop Table View (hidden on mobile) */}
            <div className="mt-4 hidden w-full space-y-4 sm:block">
              <table className="w-full">
                <thead className="w-full border-b border-[#f4feff] bg-white">
                  <tr>
                    <th className="text-md px-4 py-2 text-left font-semibold text-neutral-600">
                      Treatment
                    </th>
                    <th className="text-md px-4 py-2 text-left font-semibold text-neutral-600">
                      Dosing Instructions
                    </th>
                    <th className="text-md px-4 py-2 text-center font-semibold text-neutral-600">
                      Price
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {orderDetails?.products?.map((product, index) => (
                    <tr key={index} className="border-b border-[#f4feff]">
                      <td className="px-4 py-4">
                        <div className="flex items-start gap-2">
                          <div>
                            <p className="text-2md font-light">
                              {product?.product?.name} [{product?.quantity}]
                            </p>
                            <Badge variant="pill" size="sm">
                              {product.product?.categories?.[0]?.name}
                            </Badge>
                          </div>
                        </div>
                      </td>
                      <td className="text-2md px-4 py-4 font-light">
                        <p className="text-md w-60 font-light">{product.product.description}</p>
                      </td>
                      <td className="text-2md px-4 py-4 text-center font-light">
                        ${product.totalPrice}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View (visible only on mobile) */}
            <div className="mt-3 w-full space-y-3 sm:hidden">
              {orderDetails?.products?.map((product, index) => (
                <div
                  key={index}
                  className="rounded-lg border border-[#f4feff] bg-white p-3 shadow-sm"
                >
                  <div className="mb-2 flex items-start justify-between">
                    <div className="flex items-start gap-2">
                      <div>
                        <p className="text-md font-medium">
                          {product?.product?.name} [{product?.quantity}]
                        </p>
                        <Badge variant="pill" size="sm" className="mt-1 text-[8px]">
                          {product.product?.categories?.[0]?.name}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="mb-2">
                    <p className="mb-1 text-[10px] text-neutral-600">Dosing Instructions:</p>
                    <p className="text-[10px] font-light">{product?.product?.description || "-"}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <p className="text-md font-medium">${product.totalPrice || "-"}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <div className="flex w-full max-w-3xl flex-col items-end justify-center gap-3">
          <h1 className="font-dm-sans text-2xl">Total: ${orderDetails?.totalPrice}</h1>
          <Button
            variant="primary"
            withArrow
            className="text-md sm:text-2md w-fit min-w-20 truncate py-1.5 pr-2 pl-3 sm:min-w-24 sm:py-2 lg:min-w-32 lg:pr-2 lg:pl-4"
          >
            View invoice
          </Button>
        </div>
      </div>
    </main>
  );
};

export default MyOrderDetailPage;
