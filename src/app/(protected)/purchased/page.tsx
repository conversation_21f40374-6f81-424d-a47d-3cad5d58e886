"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import RevolvedLogo from "@/components/nav/nav-bar-logo";
import { Button } from "@/components/ui/button";
import SuccessIcon from "@/components/success-icon";

const PurchasedPage = () => {
  const router = useRouter();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex min-h-screen flex-col items-center justify-center px-4 py-8 sm:py-16"
    >
      <Link
        href="/"
        className="h-12 w-28 overflow-hidden text-center md:absolute md:top-4 md:left-10 md:mb-2 md:h-[4rem] md:w-[8rem]"
      >
        <RevolvedLogo className="h-full w-full" />
      </Link>
      <div className="w-full max-w-lg text-center 2xl:max-w-[2560px]">
        {/* Success Icon */}
        <SuccessIcon />

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col items-center space-y-6"
        >
          <h1 className="font-henju text-2xl font-normal text-black sm:text-3xl">Thank you!</h1>

          <p className="text-2md px-4 font-light text-black">
            Your order has been successfully placed, and you can track its progress anytime in the{" "}
            <Link
              href="/profile?tab=my-orders"
              className="text-primary hover:text-primary/80 text-2md font-semibold underline transition-colors"
            >
              My Orders
            </Link>{" "}
            section of your account.
          </p>

          <Button
            onClick={() => router.push("/profile?tab=my-orders")}
            withArrow
            variant="primary"
            className="text-2md w-full max-w-sm min-w-36 py-2 pr-2 pl-5"
          >
            My orders
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default PurchasedPage;
