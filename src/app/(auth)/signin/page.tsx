"use client";
import { EyeIcon, EyeOffIcon, LockIcon, MailIcon } from "@/components/icons";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FormInput } from "@/components/ui/form-input";
import { SocialButton } from "@/components/ui/social-button";
import { useToast } from "@/context/toast-context";
import { signInWithApple, signInWithGoogle } from "@/lib/firebase";

import { useAuthStore } from "@/store/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// Define the validation schema using Zod
const signinSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(1, { message: "Password is required" }),
});

// Infer the type from the schema
type SigninFormData = z.infer<typeof signinSchema>;

const SignInPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const { showToast } = useToast();
  const { login, googleLogin, appleLogin } = useAuthStore();

  // Initialize react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isSubmitSuccessful },
  } = useForm<SigninFormData>({
    resolver: zodResolver(signinSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleGoogleSignIn = async () => {
    try {
      const token = await signInWithGoogle();
      // console.log("token", token);
      await googleLogin(token, {
        onSuccess: () => {
          showToast("Signed in successfully!", "success");
          router.push("/");
        },
      });
    } catch (err: any) {
      console.error("Error during sign-in:", err);
    } finally {
    }
  };

  const handleAppleSignIn = async () => {
    try {
      const token = await signInWithApple();
      console.log("token", token);
      await appleLogin(token, {
        onSuccess: () => {
          showToast("Signed in successfully!", "success");
          router.push("/");
        },
      });
    } catch (err: any) {
      console.error("Error during sign-in:", err);
    }
  };

  const onSubmit = async (data: SigninFormData) => {
    try {
      // Call the auth store login function directly with callbacks
      await login(
        {
          email: data.email,
          password: data.password,
        },
        {
          onSuccess: () => {
            showToast("Signed in successfully!", "success");
            router.push("/");
          },
        }
      );
    } catch (error) {
      // This will only run if the callbacks don't handle the error
      showToast(
        error instanceof Error ? error.message : "An error occurred during sign in",
        "error"
      );
      if (error instanceof Error && error.message.includes("Please verify your email first")) {
        router.push(`/verify?email=${encodeURIComponent(data.email)}`);
      }
    }
  };

  const renderPasswordToggle = (show: boolean, onClick: () => void) => (
    <button
      type="button"
      onClick={onClick}
      className="absolute inset-y-0 right-0 flex items-center pr-3"
    >
      {show ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
    </button>
  );

  return (
    <>
      <div className="mt-4 flex w-full flex-col items-center gap-4 md:mt-8">
        <h1 className="font-henju text-3xl font-medium text-black md:text-5xl">Sign in</h1>
        <p className="w-full max-w-sm px-4 text-center text-md leading-normal font-normal text-[#a9a3a0] sm:max-w-xs md:w-96 md:max-w-sm md:px-0">
          By accessing your Revolved Account you can track and manage your orders and also save
          multiple addresses.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="mt-6 w-full space-y-6 md:mt-8">
        <div className="mx-auto flex w-full max-w-sm flex-col space-y-4 md:max-w-lg">
          {/* Email Field */}
          <FormInput
            type="email"
            placeholder="Email"
            icon={<MailIcon className="h-4 w-4" />}
            error={errors.email}
            register={register("email")}
          />

          {/* Password Field */}
          <FormInput
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            icon={<LockIcon className="h-4 w-4" />}
            error={errors.password}
            register={register("password")}
            rightIcon={renderPasswordToggle(showPassword, () => setShowPassword(!showPassword))}
          />
        </div>

        {/* Links */}
        <div className="mx-auto flex w-full max-w-sm flex-col items-center justify-between gap-4 sm:flex-row md:px-2 lg:max-w-lg">
          <div className="text-center text-2md font-light sm:text-left">
            Don&apos;t have an account?{" "}
            <Link
              href="/signup"
              className="text-2md font-light text-[#23a1b0] underline hover:text-[#1b8290]"
            >
              Sign up
            </Link>
          </div>
          <Link
            href="/forgot-password"
            className="text-2md font-light text-[#23a1b0] underline hover:text-[#1b8290]"
          >
            Forgot password
          </Link>
        </div>

        {/* Sign In Button */}
        <div className="flex w-full justify-center">
          <Button
            type="submit"
            variant="primary"
            withArrow
            className="w-full max-w-sm min-w-36 py-2 pr-2 pl-5"
            isLoading={isSubmitting}
            fullWidth
          >
            Login
          </Button>
        </div>

        {/* Social Login */}
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="mx-auto w-full border-t border-gray-300 md:max-w-lg" />
            </div>
            <div className="relative flex justify-center text-2md">
              <span className="bg-white px-2 text-black">Or continue with</span>
            </div>
          </div>

          <div className="mt-6 flex w-full flex-col items-center gap-3">
            <SocialButton
              icon="/icons/google.svg"
              label="Continue with Google"
              onClick={handleGoogleSignIn}
              className="w-full max-w-sm"
            />
            <SocialButton
              icon="/icons/apple.svg"
              label="Continue with Apple"
              onClick={handleAppleSignIn}
              className="w-full max-w-sm"
            />
          </div>
        </div>
      </form>
    </>
  );
};

export default SignInPage;
