"use client";
import { EyeIcon, EyeOffIcon, LockIcon, MailIcon, UserIcon } from "@/components/icons";
import { But<PERSON> } from "@/components/ui/button";
import { FormInput } from "@/components/ui/form-input";
import { useToast } from "@/context/toast-context";

import { useAuthStore } from "@/store/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const signupSchema = z
  .object({
    name: z.string().min(2, { message: "Name must be at least 2 characters" }),
    email: z.string().email({ message: "Please enter a valid email address" }),
    phone: z.string().min(10, { message: "Phone number must be at least 10 characters" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" })
      .regex(/[A-Z]/, {
        message: "Password must contain at least one uppercase letter",
      })
      .regex(/[a-z]/, {
        message: "Password must contain at least one lowercase letter",
      })
      .regex(/[0-9]/, { message: "Password must contain at least one number" }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type SignupFormData = z.infer<typeof signupSchema>;

const SignUpPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();
  const { showToast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isSubmitSuccessful },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Show validation errors
  useEffect(() => {
    if (Object.keys(errors).length > 0 && !isSubmitting) {
      const firstError = Object.values(errors)[0];
      const errorMessage = (firstError?.message as string) || "Please check the form for errors";
      showToast(errorMessage, "error");
    }
  }, [errors, isSubmitting]);

  // Show success message
  useEffect(() => {
    if (isSubmitSuccessful && !isSubmitting) {
      showToast("Account created successfully! You can now sign in.", "success");
    }
  }, [isSubmitSuccessful, isSubmitting]);

  const { signup } = useAuthStore();

  const onSubmit = async (data: SignupFormData) => {
    try {
      await signup(
        {
          name: data.name,
          email: data.email,
          password: data.password,
        },
        {
          onSuccess: () => {
            showToast("Account created! Please verify your email", "success");
            router.push(`/verify?email=${encodeURIComponent(data.email)}`);
          },
          onError: (error) => {
            showToast(error.message || "An error occurred during signup", "error");
          },
        }
      );
    } catch (error) {
      showToast(
        error instanceof Error ? error.message : "An error occurred during signup",
        "error"
      );
    }
  };

  const renderPasswordToggle = (show: boolean, onClick: () => void) => (
    <button
      type="button"
      onClick={onClick}
      className="absolute inset-y-0 right-0 flex items-center pr-3"
    >
      {show ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
    </button>
  );

  return (
    <>
      <div className="mt-4 flex flex-col items-center gap-4 md:mt-8">
        <h1 className="font-henju self-stretch text-center text-3xl font-medium text-black md:text-5xl">
          Sign up
        </h1>
        <p className="w-full max-w-sm px-4 text-center text-md leading-normal font-normal text-[#a9a3a0] md:w-96 md:px-0">
          Create your Revolved Account to track and manage your orders and save multiple addresses.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-4 md:mt-8">
        <div className="mx-auto flex w-full max-w-sm flex-col space-y-4 md:max-w-lg">
          <FormInput
            icon={<UserIcon className="h-4 w-4" />}
            placeholder="Name"
            error={errors.name}
            register={register("name")}
          />

          <FormInput
            icon={<MailIcon className="h-4 w-4" />}
            placeholder="Email"
            type="email"
            error={errors.email}
            register={register("email")}
          />

          <FormInput
            icon={<MailIcon className="h-4 w-4" />}
            placeholder="Phone Number"
            type="tel"
            error={errors.phone}
            register={register("phone")}
          />

          <FormInput
            icon={<LockIcon className="h-4 w-4" />}
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            error={errors.password}
            register={register("password")}
            rightIcon={renderPasswordToggle(showPassword, () => setShowPassword(!showPassword))}
          />

          <FormInput
            icon={<LockIcon className="h-4 w-4" />}
            type={showConfirmPassword ? "text" : "password"}
            placeholder="Confirm Password"
            error={errors.confirmPassword}
            register={register("confirmPassword")}
            rightIcon={renderPasswordToggle(showConfirmPassword, () =>
              setShowConfirmPassword(!showConfirmPassword)
            )}
          />
        </div>

        {/* Links */}
        <div className="mx-auto flex w-full items-center justify-center md:max-w-lg md:justify-start md:px-2">
          <div className="text-2md font-light">
            Already have an account?{" "}
            <Link href="/signin" className="text-[#23a1b0] underline hover:text-[#1b8290]">
              Sign in
            </Link>
          </div>
        </div>

        {/* Sign Up Button */}
        <div className="flex w-full justify-center">
          <Button
            type="submit"
            variant="primary"
            withArrow
            className="w-full max-w-sm min-w-36 py-2 pr-2 pl-5"
            isLoading={isSubmitting}
            fullWidth
          >
            Register
          </Button>
        </div>
      </form>
    </>
  );
};

export default SignUpPage;
