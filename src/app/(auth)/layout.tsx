import type { Metadata } from "next";
import RevolvedLogo from "@/components/nav/nav-bar-logo";
import Image from "next/image";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Revolved | Modern Healthcare Solutions",
  description:
    "Access personalized treatments for anti-aging, weight loss, skin care, and more. Connect with qualified healthcare practitioners for online consultations and prescription treatments.",
  keywords:
    "anti-aging treatment, weight loss, skin care, muscle support, online consultation, healthcare, medical prescriptions",
  authors: [{ name: "Revolved" }],
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <main className="font-quinn max-w-[1728px] 2xl:max-w-full">
      <div className="flex min-h-screen w-full flex-col items-start justify-between bg-white md:flex-row">
        {/* Left side - Background Image (hidden on mobile) */}
        <div className="hidden w-full md:block md:w-1/2">
          <Image
            src="/images/auth/background.jpg"
            alt="auth background"
            width={400}
            height={400}
            quality={100}
            priority
            className="h-screen w-full object-cover"
          />
        </div>

        {/* Right side - Content */}
        <div className="w-full space-y-6 px-4 pt-8 pb-8 sm:px-8 md:w-1/2 md:space-y-8 md:px-16 md:pt-[4rem] md:pb-[2rem] lg:px-[8rem]">
          {/* Header with Logo */}
          <div className="flex flex-col items-center">
            <Link
              href="/"
              className="relative h-16 w-32 overflow-hidden text-center md:h-[6rem] md:w-[12rem]"
            >
              <RevolvedLogo className="h-full w-full" />
            </Link>
          </div>

          {/* Page-specific content */}
          {children}
        </div>
      </div>
    </main>
  );
}
