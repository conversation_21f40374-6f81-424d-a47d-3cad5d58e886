"use client";

import { useAuthS<PERSON> } from "@/store/auth-store";
import { Button } from "@/components/ui/button";
import { OtpInput } from "@/components/ui/otp-input";
import { useToast } from "@/context/toast-context";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

const VerifyPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showToast } = useToast();

  const [otp, setOtp] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [resendDisabled, setResendDisabled] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number>(0);

  // Get email from query params or use a placeholder
  const email = searchParams.get("email") || "your email";

  // Handle countdown for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && resendDisabled) {
      setResendDisabled(false);
    }
  }, [countdown, resendDisabled]);

  const { verifyEmail } = useAuthStore();

  const handleVerify = async () => {
    if (otp.length !== 6) {
      showToast("Please enter a valid 6-digit code", "error");
      return;
    }

    setIsVerifying(true);

    try {
      // Call the auth store verifyEmail function directly with callbacks
      await verifyEmail(
        {
          email,
          otp,
        },
        {
          onSuccess: () => {
            showToast("Email verified successfully!", "success");
            // Redirect to home or dashboard
            router.push("/");
          },
          onError: (error) => {
            showToast(error.message || "Verification failed", "error");
          },
        }
      );
    } catch (error) {
      // This will only run if the callbacks don't handle the error
      showToast(error instanceof Error ? error.message : "Verification failed", "error");
    } finally {
      setIsVerifying(false);
    }
  };

  const { resendVerificationOtp } = useAuthStore();

  const handleResendCode = async () => {
    setResendDisabled(true);
    setCountdown(60); // 60 seconds cooldown

    try {
      // Call the auth store resendVerificationOtp function directly with callbacks
      await resendVerificationOtp(email, {
        onSuccess: () => {
          showToast("Verification code resent successfully", "success");
        },
        onError: (error) => {
          showToast(error.message || "Failed to resend code", "error");
          setResendDisabled(false);
          setCountdown(0);
        },
      });
    } catch (error) {
      // This will only run if the callbacks don't handle the error
      showToast(error instanceof Error ? error.message : "Failed to resend code", "error");
      setResendDisabled(false);
      setCountdown(0);
    }
  };

  return (
    <>
      <div className="mt-4 flex flex-col items-center gap-4 md:mt-8">
        <h1 className="font-henju self-stretch text-center text-3xl font-medium text-black md:text-5xl">
          Verify Email
        </h1>
        <p className="w-full max-w-sm px-4 text-center text-md leading-normal font-normal text-[#a9a3a0] md:w-96 md:px-0">
          We&apos;ve sent a verification code to <span className="font-medium">{email}</span>
        </p>
      </div>

      {/* OTP Input */}
      <div className="mx-auto mt-6 w-full max-w-sm md:mt-8 md:max-w-lg">
        <p className="mb-6 text-center text-md font-normal text-[#a9a3a0]">
          Enter the 6-digit verification code
        </p>
        <OtpInput length={6} onComplete={setOtp} disabled={isVerifying} className="mb-8" />
      </div>

      {/* Verify Button */}
      <div className="mx-auto w-full max-w-sm">
        <Button
          withArrow
          variant="primary"
          onClick={handleVerify}
          isLoading={isVerifying}
          fullWidth
          className="w-full max-w-sm min-w-36 py-2 pr-2 pl-5"
        >
          Verify Email
        </Button>
      </div>

      {/* Resend Code */}
      <div className="text-center">
        <p className="text-md font-normal text-[#a9a3a0]">
          Didn&apos;t receive the code?{" "}
          {resendDisabled ? (
            <span className="text-gray-500">Resend in {countdown}s</span>
          ) : (
            <button
              type="button"
              onClick={handleResendCode}
              className="font-medium text-[#23a1b0] underline hover:text-[#1b8290]"
            >
              Resend Code
            </button>
          )}
        </p>
      </div>

      {/* Back to Sign Up */}
      <div className="text-center">
        <Link
          href="/signup"
          className="text-md font-normal text-[#23a1b0] underline hover:text-[#1b8290]"
        >
          Back to Sign Up
        </Link>
      </div>

      {/* Demo Note */}
      <div className="mx-auto mt-4 w-full max-w-sm rounded-md bg-gray-50 p-4">
        <p className="text-md text-gray-600">
          <strong>Demo Note:</strong> For testing purposes, the verification code is
          &quot;123456&quot;.
        </p>
      </div>
    </>
  );
};

export default VerifyPage;
