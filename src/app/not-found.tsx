"use client";

import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";

export default function NotFound() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="mt-16 flex min-h-[80vh] flex-col items-center justify-center px-4 py-12 sm:px-6 md:mt-24 lg:flex-row lg:px-8"
    >
      {/* Content Section */}
      <div className="w-full max-w-lg text-center lg:w-1/2 lg:pr-8 lg:text-left">
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="space-y-6"
        >
          {/* Error Code */}
          <p className="font-dm_sans text-lg font-medium text-[#23a1b0]">Error 404</p>

          {/* Title */}
          <h1 className="text-4xl font-medium text-black sm:text-5xl">Page not found</h1>

          {/* Description */}
          <p className="font-dm_sans text-2md text-neutral-600 sm:text-lg">
            We couldn&apos;t find the page you&apos;re looking for. It might have been moved,
            deleted, or never existed.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col gap-4 pt-4 sm:flex-row">
            <Link
              href="/"
              className="flex w-full items-center justify-center rounded-full bg-[#23a1b0] px-6 py-3 text-2md text-white transition-colors duration-200 hover:bg-[#1c8a97] sm:w-auto"
            >
              Back to Home
            </Link>
            <Link
              href="/contact"
              className="flex w-full items-center justify-center rounded-full border border-[#40bfaa] px-6 py-3 text-2md text-[#40bfaa] transition-colors duration-200 hover:bg-[#40bfaa]/5 sm:w-auto"
            >
              Contact Support
            </Link>
          </div>
        </motion.div>
      </div>

      {/* Image Section */}
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
        className="mt-12 w-full lg:mt-0 lg:w-1/2"
      >
        <div className="relative mx-auto aspect-square w-full max-w-lg">
          {/* Background Pattern */}
          <div className="absolute inset-0 animate-pulse rounded-full bg-[#40bfa9]/10" />

          {/* Main Image */}
          <Image
            src="/icons/logo-icon.svg" // Make sure to add this image to your public folder
            alt="Page not found illustration"
            fill
            className="object-contain p-8"
            priority
          />

          {/* Decorative Elements */}
          <motion.div
            animate={{
              rotate: [0, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            }}
            className="absolute -top-4 -right-4 h-12 w-12"
          >
            <svg viewBox="0 0 24 24" fill="none" className="h-full w-full text-[#23a1b0]">
              <path
                d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  );
}
