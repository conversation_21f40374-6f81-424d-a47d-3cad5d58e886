'use client';

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { mutate } from 'swr';
import {
  AuthState,
  LoginCredentials,
  SignupCredentials,
  VerifyEmailCredentials
} from '@/types/auth';
import { authService } from '@/api/auth-service';
import { useBagStore } from '@/store/bag-store';
import { addBagItem } from '@/api/bag-service';

interface AuthCallbacks {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

// Helper function to sync local cart items with the server after login
const syncLocalCartWithServer = async () => {
  try {
    // Get local cart items from the bag store
    const bagStore = useBagStore.getState();
    const localItems = bagStore.items;

    // Only process items that were created locally (have local_ prefix in ID)
    const localOnlyItems = localItems.filter(item =>
      item._id?.startsWith('local_')
    );

    // If there are local items, add them to the server
    if (localOnlyItems.length > 0) {
      // Add each local item to the server
      for (const item of localOnlyItems) {
        await addBagItem({
          productId: item.productId?._id,
          quantity: item.quantity || 1,
          isPrescription: item.isPrescription,
          prescriptionImage: item.prescriptionImage,
          bookinId: item.bookinId,
          prescribedByDoctor: item.prescribedByDoctor
        });
      }

      // After adding all items, revalidate the bag list to get the server items
      // This will be handled by the getBagItemList hook in the component
    }
  } catch (error) {
    console.error('Error syncing local cart with server:', error);
  }
};

interface AuthStore extends AuthState {
  // Auth actions
  login: (credentials: LoginCredentials, callbacks?: AuthCallbacks) => Promise<void>;
  googleLogin: (idToken: string, callbacks?: AuthCallbacks) => Promise<void>;
  appleLogin: (idToken: string, callbacks?: AuthCallbacks) => Promise<void>;
  signup: (credentials: SignupCredentials, callbacks?: AuthCallbacks) => Promise<void>;
  verifyEmail: (credentials: VerifyEmailCredentials, callbacks?: AuthCallbacks) => Promise<void>;
  resendVerificationOtp: (email: string, callbacks?: AuthCallbacks) => Promise<void>;
  logout: (callbacks?: AuthCallbacks) => void;
  refreshAuth: (callbacks?: AuthCallbacks) => Promise<boolean>;
  clearError: () => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      accessToken: null,
      refreshToken: null,
      isLoading: false,
      isAuthenticated: false,
      error: null,

      // Login action
      login: async (credentials, callbacks) => {
        try {
          set({ isLoading: true, error: null });
          const response = await authService.login(credentials);
          if (response) {
            console.log("get the signin response", response);

            set({
              user: response?.data?.user,
              accessToken: response?.data?.accessToken,
              refreshToken: response?.data?.refreshToken,
              isAuthenticated: true,
              isLoading: false,
            });

            // Sync local cart items with server after successful login
            await syncLocalCartWithServer();

            // Revalidate SWR cache for user data
            // mutate('auth/me');

            // Call onSuccess callback if provided
            callbacks?.onSuccess?.();
          }

        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to login',
            isAuthenticated: false,
          });

          // Call onError callback if provided
          callbacks?.onError?.(error);
          throw error;
        }
      },

      googleLogin: async (idToken, callbacks) => {
        try {
          set({ isLoading: true, error: null });
          const response = await authService.googleLogin(idToken);
          if (response) {
            console.log("get the google signin response", response);

            set({
              user: response?.data?.user,
              accessToken: response?.data?.accessToken,
              refreshToken: response?.data?.refreshToken,
              isAuthenticated: true,
              isLoading: false,
            });

            // Sync local cart items with server after successful login
            await syncLocalCartWithServer();

            callbacks?.onSuccess?.();
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to login with Google',
            isAuthenticated: false,
          });

          // Call onError callback if provided
          callbacks?.onError?.(error);
          throw error;
        }
      },

      appleLogin: async (idToken, callbacks) => {
        try {
          set({ isLoading: true, error: null });
          const response = await authService.appleLogin(idToken);
          if (response) {
            console.log("get the apple signin response", response);

            set({
              user: response?.data?.user,
              accessToken: response?.data?.accessToken,
              refreshToken: response?.data?.refreshToken,
              isAuthenticated: true,
              isLoading: false,
            });

            // Sync local cart items with server after successful login
            await syncLocalCartWithServer();

            callbacks?.onSuccess?.();
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to login with Apple',
            isAuthenticated: false,
          });

          // Call onError callback if provided
          callbacks?.onError?.(error);
          throw error;
        }
      },

      // Signup action
      signup: async (credentials, callbacks) => {
        try {
          set({ isLoading: true, error: null });
          const response = await authService.signup(credentials);
          set({
            user: response?.data?.user,
            accessToken: response?.data?.accessToken,
            refreshToken: response?.data?.refreshToken,
            isAuthenticated: true,
            isLoading: false,
          });

          // Sync local cart items with server after successful signup
          await syncLocalCartWithServer();

          // Revalidate SWR cache for user data
          // mutate('auth/me');

          // Call onSuccess callback if provided
          callbacks?.onSuccess?.();
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to signup',
            isAuthenticated: false,
          });

          // Call onError callback if provided
          callbacks?.onError?.(error);
          throw error;
        }
      },

      // Verify email action
      verifyEmail: async (credentials, callbacks) => {
        try {
          set({ isLoading: true, error: null });
          const response = await authService.verifyEmail(credentials);
          set({
            user: response?.data?.user,
            accessToken: response?.data?.accessToken,
            refreshToken: response?.data?.refreshToken,
            isAuthenticated: true,
            isLoading: false,
          });

          // Sync local cart items with server after successful verification
          await syncLocalCartWithServer();

          // Revalidate SWR cache for user data
          // mutate('auth/me');

          // Call onSuccess callback if provided
          callbacks?.onSuccess?.();
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to verify email',
          });

          // Call onError callback if provided
          callbacks?.onError?.(error);
          throw error;
        }
      },

      // Logout action
      logout: (callbacks) => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
          error: null,
        });

        // Clear SWR cache
        // mutate('auth/me', null, false);

        // Call onSuccess callback if provided
        callbacks?.onSuccess?.();
      },

      // Refresh authentication
      refreshAuth: async (callbacks) => {
        const { accessToken } = get();
        if (!accessToken) return false;

        try {
          set({ isLoading: true, error: null });
          const response = await authService.refreshToken(accessToken);
          set({
            user: response?.data?.user,
            accessToken: response?.data?.accessToken,
            refreshToken: response?.data?.refreshToken,
            isAuthenticated: true,
            isLoading: false,
          });

          // Revalidate SWR cache for user data
          // mutate('auth/me');

          // Call onSuccess callback if provided
          callbacks?.onSuccess?.();
          return true;
        } catch (error: any) {
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message || 'Session expired',
          });

          // Clear SWR cache
          // mutate('auth/me', null, false);

          // Call onError callback if provided
          callbacks?.onError?.(error);
          return false;
        }
      },

      // Resend verification OTP
      resendVerificationOtp: async (email, callbacks) => {
        try {
          set({ isLoading: true, error: null });
          await authService.resendVerificationOtp(email);
          set({ isLoading: false });

          // Call onSuccess callback if provided
          callbacks?.onSuccess?.();
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to resend verification code',
          });

          // Call onError callback if provided
          callbacks?.onError?.(error);
          throw error;
        }
      },

      // Clear error
      clearError: () => set({ error: null }),
    }),
    {
      name: process.env.NEXT_PUBLIC_AUTH_STORAGE_KEY || 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
