import { apiRequest } from '../lib/api';
import {
  AuthResponse,
  LoginCredentials,
  SignupCredentials,
  VerifyEmailCredentials,
} from '@/types/auth';
import { API_ENDPOINTS } from './api-endpoints';

/**
 * Authentication service for handling auth-related API requests
 */
export const authService = {
  /**
   * Register a new user
   */
  async signup(credentials: SignupCredentials): Promise<AuthResponse> {
    return apiRequest<AuthResponse>(API_ENDPOINTS.auth.register, {
      method: 'POST',
      data: credentials,
    });
  },

  /**
   * Verify email with OTP
   */
  async verifyEmail(credentials: VerifyEmailCredentials): Promise<AuthResponse> {
    return apiRequest<AuthResponse>(API_ENDPOINTS.auth.verifyEmail, {
      method: 'POST',
      data: credentials,
    });
  },

  /**
   * Login a user
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return apiRequest<AuthResponse>(API_ENDPOINTS.auth.login, {
      method: 'POST',
      data: credentials,
    });
  },

  async googleLogin(idToken: string): Promise<AuthResponse> {
    return apiRequest<AuthResponse>(API_ENDPOINTS.auth.googleLogin, {
      method: 'POST',
      data: { idToken },
    });
  },

  async appleLogin(idToken: string): Promise<AuthResponse> {
    return apiRequest<AuthResponse>(API_ENDPOINTS.auth.appleLogin, {
      method: 'POST',
      data: { idToken },
    });
  },

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    return apiRequest<AuthResponse>(API_ENDPOINTS.auth.refreshToken, {
      method: 'POST',
      data: { refreshToken },
    });
  },

  /**
   * Resend verification OTP
   */
  async resendVerificationOtp(email: string): Promise<{ message: string }> {
    return apiRequest<{ message: string }>(API_ENDPOINTS.auth.resendVerificationOtp, {
      method: 'POST',
      data: { email },
    });
  },
};
