'use client';

import { useMemo } from 'react';
import useS<PERSON>, { mutate } from 'swr';

import { fetcher, revolvedCreator, revolvedCreatorPutMethod, revolvedSmasher } from '@/lib/axios';

import { useBagStore } from '@/store/bag-store';
import { IBagItemResponse } from '@/types/bag';
import { API_ENDPOINTS } from './api-endpoints';

// Helper function to check if user is authenticated
const isUserAuthenticated = (): boolean => {
    // Access auth store directly (not using hook since this is outside React component)
    let isAuthenticated = false;

    if (typeof window !== 'undefined') {
        const authState = localStorage.getItem('auth-storage');
        if (authState) {
            try {
                const { state } = JSON.parse(authState);
                isAuthenticated = state?.isAuthenticated || false;
            } catch (error) {
                console.error('Error parsing auth state:', error);
            }
        }
    }

    return isAuthenticated;
};

const BAG_ITEMS_KEY = API_ENDPOINTS.bag.getList;

export function getBagItemList() {
    const setBagItems = useBagStore((state) => state.setBagItems);
    const items = useBagStore((state) => state.items);

    // Check if user is authenticated
    const authenticated = isUserAuthenticated();

    // For unauthenticated users, we don't need to fetch from API
    const shouldFetch = authenticated;

    const { data, isLoading, error, isValidating } = useSWR<IBagItemResponse>(
        shouldFetch ? BAG_ITEMS_KEY : null, // Only fetch if authenticated
        fetcher,
        {
            revalidateOnFocus: false,
            dedupingInterval: 5000,
            onSuccess: (data) => {
                if (data?.data) {
                    setBagItems(data.data);
                }
            },
            keepPreviousData: true,
        }
    );

    const revalidateBagItemList = () => {
        if (authenticated) {
            mutate(BAG_ITEMS_KEY);
        }
    };

    // Use useMemo to memoize the return value
    const memoizedValue = useMemo(
        () => {
            // For unauthenticated users, use the items from the store directly
            if (!authenticated) {
                return {
                    bagItems: items,
                    bagItemsLoading: false,
                    bagItemsError: null,
                    bagItemsValidating: false,
                    bagItemsEmpty: items.length === 0,
                };
            }

            // For authenticated users, use the data from the API
            return {
                bagItems: data?.data || [],
                bagItemsLoading: isLoading,
                bagItemsError: error,
                bagItemsValidating: isValidating,
                bagItemsEmpty: !isLoading && !isValidating && !data?.data?.length,
            };
        },
        [authenticated, data?.data, error, isLoading, isValidating, items]
    );

    return {
        ...memoizedValue,
        revalidateBagItemList,
    };
}

export function addBagItem(bagItem: any) {
    const url = API_ENDPOINTS.bag.add;
    const result = revolvedCreator([url, bagItem]);
    return result;
}

export function updateBagItem(bagItem: any, id: string) {
    const url = `${API_ENDPOINTS.bag.update}/${id}`;
    const result = revolvedCreatorPutMethod([url, bagItem]);
    return result;
}

export function deleteBagItem(id: string) {
    const url = `${API_ENDPOINTS.bag.delete}/${id}`;
    const result = revolvedSmasher(url);
    return result;
}

export function clearBag() {
    const url = API_ENDPOINTS.bag.clear;
    const result = revolvedSmasher(url);
    return result;
}
