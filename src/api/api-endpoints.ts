export const API_ENDPOINTS = {
  auth: {
    register: "/auth/register",
    verifyEmail: "/auth/verify-email-by-otp",
    login: "/auth/login-user",
    autoLogin: "/auth/auto-login",
    refreshToken: "/auth/refresh-token",
    resendVerificationOtp: "/auth/resend-verification-otp",
    googleLogin: "/auth/google",
    appleLogin: "/auth/apple",
  },
  products: {
    publicGet: "/product/public/get",
    publicGetByCategory: "/product/public/get-by-category",
  },
  category: {
    publicHomeGet: "category/list/public-list",
    publicProductCategoryGet: "/category/list/public-product-category",
    publicGetBySlug: "/category/slug/",
    publicGetById: "/category/public/",
  },
  questionnaire: {
    publicGet: "/questionnaire/public/get",
    submitPrebookingAnswer: "/questionnaire/consumer/submit-pre-booking-answers",
    getPrebookingAnswers: "/questionnaire/consumer/get-my-pre-booking-answers",
  },
  address: {
    create: "consumer/address",
    update: "consumer/address",
    list: "consumer/address/",
    delete: "consumer/address/",
    getById: "consumer/address/",
  },
  bag: {
    add: "/cart/add",
    getList: "/cart/get-cart",
    update: "/cart/update",
    delete: "/cart/delete",
    clear: "/cart/clear",
  },
  booking: {
    create: "/consumer/booking/create",
    getList: "/consumer/booking/get-my-bookings/",
    getDetail: "/consumer/booking/get-booking",
    cancelBooking: "/consumer/booking/",
  },
  order: {
    create: "/consumer/order",
    getList: "/consumer/order/my-orders",
    getDetails: "/consumer/order"
  }
};
