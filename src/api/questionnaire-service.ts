import { fetcher, revolvedCreator } from "@/lib/axios";
import {
  PrebookingAnswersGetResponse,
  QuestionnariesPublicGetResponse,
} from "@/types/questionnaire";
import { useMemo } from "react";
import useSWR, { mutate, SWRConfiguration } from "swr";
import { API_ENDPOINTS } from "./api-endpoints";

const swrOptions: SWRConfiguration = {
  // revalidateIfStale: false,
  revalidateOnFocus: false,
  // revalidateOnReconnect: false,
};

interface GetPublicCategoriesParams {
  limit?: number;
  searchQuery?: string;
  page?: number;
  category: string;
  bookingPhase?: "prebooking" | "postbooking";
}

interface GetPrebookingAnswersParams {
  limit?: number;
  page?: number;
}

export function useGetPublicQuestionnaires({
  page = 0,
  limit = 100,
  searchQuery = "",
  category,
  bookingPhase,
}: GetPublicCategoriesParams) {
  const getTheFullUrl = () => {
    const queryParams: { [key: string]: any } = {
      page: page + 1, // API uses 1-based indexing
      limit,
      category,
    };

    if (searchQuery !== "") queryParams.search = searchQuery;
    if (bookingPhase) queryParams.bookingPhase = bookingPhase;

    return `${API_ENDPOINTS.questionnaire.publicGet}?${new URLSearchParams(queryParams)}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<QuestionnariesPublicGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      questionnaires: data?.data?.data || [],
      questionnairesLoading: isLoading,
      questionnairesError: error,
      questionnairesValidating: isValidating,
      questionnairesEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
      totalCount: data?.data?.totalCount ?? 0,
      totalPages: data?.data?.totalPages ?? 0,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateQuestionnaires = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidateQuestionnaires,
  };
}

export function useGetPrebookingAnswers({ page = 0, limit = 100 }: GetPrebookingAnswersParams) {
  const getTheFullUrl = () => {
    const queryParams: { [key: string]: any } = {
      page: page + 1,
      limit,
    };

    return `${API_ENDPOINTS.questionnaire.getPrebookingAnswers}?${new URLSearchParams(queryParams)}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<PrebookingAnswersGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      prebookingAnswers: data?.data?.data || [],
      prebookingAnswersLoading: isLoading,
      prebookingAnswersError: error,
      prebookingAnswersValidating: isValidating,
      prebookingAnswersEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
      totalCount: data?.data?.totalCount ?? 0,
      totalPages: data?.data?.totalPages ?? 0,
      hasAnsweredAll: data?.data?.hasAnsweredAll ?? false,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidatePrebookingAnswers = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidatePrebookingAnswers,
  };
}

export function submitPrebookingAnswer(answer: any) {
  const url = API_ENDPOINTS.questionnaire.submitPrebookingAnswer;
  const result = revolvedCreator([url, answer]);
  return result;
}
