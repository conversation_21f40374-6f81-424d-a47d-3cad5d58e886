import axiosInstance, {
  fetcher,
  revolvedCreator,
  revolvedCreatorPutMethod,
  revolvedPatcher,
} from "@/lib/axios";
import { API_ENDPOINTS } from "./api-endpoints";
import useSWR, { mutate, SWRConfiguration } from "swr";
import {
  BookingDetailGetResponse,
  BookingsGetResponse,
  BookingStatus,
  PaymentStatus,
} from "@/types/booking";
import { useMemo } from "react";

const swrOptions: SWRConfiguration = {
  // revalidateIfStale: false,
  revalidateOnFocus: false,
  // revalidateOnReconnect: false,
};

interface GetBookingsParams {
  page?: number;
  limit?: number;
  bookingStatus?: BookingStatus;
  paymentStatus?: PaymentStatus;
}

export function useGetBookings({
  page = 0,
  limit = 100,
  bookingStatus,
  paymentStatus,
}: GetBookingsParams) {
  const getTheFullUrl = () => {
    const queryParams: { [key: string]: any } = {
      page: page + 1, // API uses 1-based indexing
      limit,
    };

    if (bookingStatus) queryParams.bookingStatus = bookingStatus;
    if (paymentStatus) queryParams.paymentStatus = paymentStatus;

    return `${API_ENDPOINTS.booking.getList}?${new URLSearchParams(queryParams)}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<BookingsGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      bookingDetails: data?.data?.data || [],
      bookingDetailsLoading: isLoading,
      bookingDetailsError: error,
      bookingDetailsValidating: isValidating,
      bookingDetailsEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
      totalCount: data?.data?.totalCount ?? 0,
      totalPages: data?.data?.totalPages ?? 0,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateBookingDetails = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidateBookingDetails,
  };
}

export function useGetBookingDetail({ bookingId }: { bookingId: string }) {
  const getTheFullUrl = () => {
    if (!bookingId) return null;

    return `${API_ENDPOINTS.booking.getDetail}/${bookingId}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<BookingDetailGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      bookingDetail: data?.data,
      bookingDetailLoading: isLoading,
      bookingDetailError: error,
      bookingDetailValidating: isValidating,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateBookingDetail = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidateBookingDetail,
  };
}

export async function createBooking(bookingData: any) {
  const url = API_ENDPOINTS.booking.create;
  const result = revolvedCreator([url, bookingData]);
  return result;
}

export async function cancelBooking(bookingId: string) {
  const url = `${API_ENDPOINTS.booking.cancelBooking}${bookingId}/cancel`;
  const result = revolvedPatcher(url);
  return result;
}
