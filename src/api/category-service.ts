import { fetcher } from "@/lib/axios";
import { CategoriesGetResponse, TreatmentGetResponse } from "@/types/category";
import { useMemo } from "react";
import useSWR, { mutate, SWRConfiguration } from "swr";
import { API_ENDPOINTS } from "./api-endpoints";

const swrOptions: SWRConfiguration = {
  // revalidateIfStale: false,
  revalidateOnFocus: false,
  // revalidateOnReconnect: false,
};

interface GetPublicCategoriesParams {
  limit?: number;
  searchQuery?: string;
  page?: number;
}

export function useGetPublicCategories({
  page,
  limit,
  searchQuery = "",
}: GetPublicCategoriesParams) {
  const getTheFullUrl = () => {
    const queryParams: { [key: string]: any } = {
      page: page + 1, // API uses 1-based indexing
      limit,
    };

    if (searchQuery !== "") queryParams.search = searchQuery;

    return `${API_ENDPOINTS.category.publicHomeGet}?${new URLSearchParams(queryParams)}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<CategoriesGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      categories: data?.data?.data || [],
      categoriesLoading: isLoading,
      categoriesError: error,
      categoriesValidating: isValidating,
      categoriesEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
      totalCount: data?.data?.totalCount ?? 0,
      totalPages: data?.data?.totalPages ?? 0,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateCategories = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidateCategories,
  };
}

export function useGetPublicProductCategories({
  page,
  limit,
  searchQuery = "",
}: GetPublicCategoriesParams) {
  const getTheFullUrl = () => {
    const queryParams: { [key: string]: any } = {
      page: page + 1, // API uses 1-based indexing
      limit,
    };

    if (searchQuery !== "") queryParams.search = searchQuery;

    return `${API_ENDPOINTS.category.publicProductCategoryGet}?${new URLSearchParams(queryParams)}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<CategoriesGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      productCategories: data?.data?.data || [],
      productCategoriesLoading: isLoading,
      productCategoriesError: error,
      productCategoriesValidating: isValidating,
      productCategoriesEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
      totalCount: data?.data?.totalCount ?? 0,
      totalPages: data?.data?.totalPages ?? 0,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidatePublicCategories = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidatePublicCategories,
  };
}

export function useGetTreatmentBySlug({ slug }: { slug: string }) {
  const getTheFullUrl = () => {
    return `${API_ENDPOINTS.category.publicGetBySlug}${slug}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<TreatmentGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      categoryDetail: data?.data,
      categoryDetailLoading: isLoading,
      categoryDetailError: error,
      categoryDetailValidating: isValidating,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateCategoryDetail = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidateCategoryDetail,
  };
}

export function useGetTreatmentById({ id, requestDate }: { id: string; requestDate?: string }) {
  const getTheFullUrl = () => {
    const queryParams: { [key: string]: any } = {};
    if (requestDate !== "") queryParams.requestDate = requestDate;
    return `${API_ENDPOINTS.category.publicGetById}${id}?${new URLSearchParams(queryParams)}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<TreatmentGetResponse>(
    getTheFullUrl,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      categoryDetail: data?.data,
      categoryDetailLoading: isLoading,
      categoryDetailError: error,
      categoryDetailValidating: isValidating,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateCategoryDetail = () => {
    mutate(getTheFullUrl());
  };

  return {
    ...memoizedValue,
    revalidateCategoryDetail,
  };
}
