import { fetcher, revolvedCreator, revolvedCreator<PERSON><PERSON><PERSON>ethod, revolvedSmasher } from "@/lib/axios";
import { API_ENDPOINTS } from "./api-endpoints";
import useSWR, { mutate, SWRConfiguration } from "swr";
import { useMemo } from "react";
import { Address, AddressListResponse, GetAddressParams } from "@/types/address";

const swrOptions: SWRConfiguration = {
  revalidateOnFocus: false,
};

function getAddressListUrl(params: GetAddressParams = {}) {
  const queryParams: { [key: string]: any } = {};

  if (typeof params.page === "number") queryParams.page = params.page;
  if (typeof params.limit === "number") queryParams.limit = params.limit;

  const searchParams = new URLSearchParams(queryParams).toString();
  return `${API_ENDPOINTS.address.list}${searchParams ? `?${searchParams}` : ""}`;
}

// fetch address list
export function useGetAddresses({ page, limit }: GetAddressParams = {}) {
  const getUrl = () => {
    const queryParams: { [key: string]: any } = {};

    if (typeof page === "number") {
      queryParams.page = page;
    }

    if (typeof limit === "number") {
      queryParams.limit = limit;
    }

    const searchParams = new URLSearchParams(queryParams).toString();

    return `${API_ENDPOINTS.address.list}${searchParams ? `?${searchParams}` : ""}`;
  };

  const { data, isLoading, error, isValidating } = useSWR<AddressListResponse>(
    getUrl,
    fetcher,
    swrOptions
  );

  const memoized = useMemo(
    () => ({
      addresses: data?.data?.data || [],
      addressesLoading: isLoading,
      addressesError: error,
      addressesValidating: isValidating,
      addressesEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
      totalCount: data?.data?.totalCount ?? 0,
      totalPages: data?.data?.totalPages ?? 0,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateAddresses = () => {
    mutate(getUrl());
  };

  return {
    ...memoized,
    revalidateAddresses,
  };
}

// Fetch a single address by ID
export function useGetAddressById(id: string) {
  const url = id ? `${API_ENDPOINTS.address.getById}${id}` : "";

  const { data, isLoading, error, isValidating } = useSWR<{ data: Address }>(
    id ? url : null,
    fetcher,
    swrOptions
  );

  const memoized = useMemo(
    () => ({
      address: data?.data,
      addressLoading: isLoading,
      addressError: error,
      addressValidating: isValidating,
      addressEmpty: !isLoading && !isValidating && !data?.data,
    }),
    [data?.data, error, isLoading, isValidating]
  );

  const revalidateAddress = () => {
    mutate(url);
  };

  return {
    ...memoized,
    revalidateAddress,
  };
}

// Function to create a new address
export async function createAddress(payload: Partial<Address>, params: GetAddressParams = {}) {
  const res = await revolvedCreator([API_ENDPOINTS.address.create, payload]);
  await mutate(getAddressListUrl(params));
  return res;
}

// Function to update an existing address
export async function updateAddress(
  id: string,
  payload: Partial<Address>,
  params: GetAddressParams = {}
) {
  const res = await revolvedCreatorPutMethod([`${API_ENDPOINTS.address.update}/${id}`, payload]);
  await mutate(getAddressListUrl(params));
  return res;
}

// Function to delete an address
export async function deleteAddress(id: string, params: GetAddressParams = {}) {
  const res = await revolvedSmasher([`${API_ENDPOINTS.address.delete}${id}`, {}]);
  await mutate(getAddressListUrl(params));
  return res;
}
