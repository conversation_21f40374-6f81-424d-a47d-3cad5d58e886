import { IProductSingleItem } from "./product";

export interface BagItem {
  id: string;
  name: string;
  category: string;
  price: number;
  quantity?: number;
}

export interface IBagItem {
  _id?: string;
  userId?: string;
  productId?: IProductSingleItem;
  quantity?: number;
  isPrescription: boolean;
  prescriptionImage?: string;
  bookinId?: string;
  prescribedByDoctor?: string;
}

export interface IBagItemResponse {
  data: IBagItem[];
  message: string;
}

export interface Discount {
  code: string;
  amount: number;
  type: 'percentage' | 'fixed';
}

export interface BagState {
  items: IBagItem[];
  discount: Discount | null;
  discountCode: string;
  deliveryFee: number;
}
