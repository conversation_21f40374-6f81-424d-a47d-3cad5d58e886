import { User } from "./auth";
import { ICategoryItem } from "./category";

export type BookingStatus = "pending" | "confirmed" | "completed" | "cancelled";
export type PaymentStatus = "pending" | "paid" | "failed" | "cancelled" | "refunded";

export interface BookingsGetResponse {
  message: string;
  data: {
    data: BookingItem[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  };
}

export interface BookingDetailGetResponse {
  message: string;
  data: BookingItem;
}

export interface BookingItem {
  _id: string;
  user: string;
  category: ICategoryItem;
  doctor: IDoctorItem;
  bookingTime: string;
  duration: number;
  referenceId: string;
  status: BookingStatus;
  totalPrice: number;
  discountPrice: number;
  additionalServiceFee: number;
  taxAmount: number;
  paymentStatus: PaymentStatus;
  transactionId: string | null;
  deletedAt: string | null;
  treatmentPlan: any[];
  createdAt: string;
  updatedAt: string;
  doctorProfile?: {
    education: string;
  };
  postQuestionnaireCompletion?: {
    percentage: number;
    isCompleted: boolean;
  };
  __v: number;
}

export interface IDoctorItem {
  _id: string;
  phoneNumber: string;
  email: string;
  password: string;
  name: string;
  wallet: number;
  isVerified: boolean;
  roles: any[];
  userType: string;
  doctorProfile: string;
  createdAt: string;
  updatedAt: string;
  image?: string;
  __v: number;
}

export interface BookingSingleItem {
  _id: string;
  user: User;
  category: ICategoryItem;
  doctor: IDoctorItem;
  bookingTime: string;
  duration: number;
  referenceId: string;
  status: BookingStatus;
  totalPrice: number;
  discountPrice: number;
  additionalServiceFee: number;
  taxAmount: number;
  paymentStatus: PaymentStatus;
  transactionId: string | null;
  deletedAt: string | null;
  treatmentPlan: any[];
  createdAt: string;
  updatedAt: string;
  __v: number;
}
