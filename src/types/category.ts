export type ICategoryItem = {
  id: string;
  _id?: string;
  cover_image: any;
  name: string;
  slug: string;
  description: string;
  isActive: any;
  deletedAt: any;
  createdAt: string;
  updatedAt: string[];
  category_details?: ICategoryDetailItem[];
  timeSlots?: string[];
};

export type ICategoryDetailItem = {
  title: string;
  description: string;
};

export type ICategoryForm = {
  cover_image: any;
  name: string;
  description: string;
  isActive: any;
  category_details: ICategoryDetailItem[];
};

export interface CategoriesGetResponse {
  data: {
    data: ICategoryItem[];
    totalCount: number;
    totalPages: number;
  };
}

export interface TreatmentGetResponse {
  message: string;
  data: ICategoryItem;
}

export interface TreatmentContent {
  title: string;
  description: string;
  icon: string;
  image: string;
  href: string;
  qHref: string;
}
