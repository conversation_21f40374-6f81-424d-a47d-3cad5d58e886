import { IProductSingleItem } from "./product";

export interface CreateOrderData {
    deliveryAddress: string;
    paymentMethod: string;
    notes?: string;
}


export interface OrderItem {
    name: string;
    quantity: number;
}

export type OrderStatus = "placed" | "confirmed" | "processing" | "shipped" | "delivered" | "cancelled";
export type PaymentStatus = "pending" | "paid" | "failed" | "cancelled" | "refunded";
export type FulfillmentStatus = "pending" | "processing" | "shipped" | "delivered" | "cancelled";

export interface UserDetails {
    _id: string;
    email: string;
    name: string;
}

export interface Fulfillment {
    status: FulfillmentStatus;
    shippingCost: number;
    shippingMethod?: string;
    _id: string;
}

interface ProductDetails {
    product: IProductSingleItem;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    _id: string;
}

export interface Order {
    _id: string;
    user: string;
    deliveryAddress: {
        _id: string;
        additionalInfo: string;
        city: string;
        contactName: string;
        createdAt: string;
        state: string;
        street: string;
        zipCode: string;
        __v: number;
        user: string;
    };
    orderReference: string;
    totalPrice: number;
    subTotal: number;
    discountPrice: number;
    taxAmount: number;
    additionalCharges: number;
    paymentStatus: PaymentStatus;
    status: OrderStatus;
    fulfillment: Fulfillment;
    products?: ProductDetails[];
    createdAt: string;
    updatedAt: string;
    __v: number;
    userDetails?: UserDetails[];
}

export interface OrdersGetResponse {
    message: string;
    data: {
        data: Order[];
        totalCount: number;
        totalPages: number;
        currentPage: number;
    };
}

export interface OrderDetailGetResponse {
    message: string;
    data: Order;
}
