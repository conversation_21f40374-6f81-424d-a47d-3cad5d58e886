export type IProductSingleItem = {
  _id?: string;
  name: string;
  description?: string;
  main_image: string | null;
  images: string[];
  categories: Array<{ _id: string, name?: string }>;
  relatedProducts?: Array<{ _id: string, name: string, main_image?: string, price?: number }> | string[];
  isActive: boolean;
  isPublic: boolean;
  price: number;
  minOrderQuantity: number;
  maxOrderQuantity: number;
  tags: string[];
  stock: number;
  rating: number;
  slug: string;
  productDetails: Array<{
    _id: string;
    title: string;
    description: string;
  }>;
  clinicalDetails: Array<{
    _id: string;
    title: string;
    contents: string[];
    images: Array<{
      _id: string;
      title: string;
      imageUrl: string;
    }>;
  }>;
  compounds: Array<{
    _id: string;
    name: string;
    concentration: string;
  }>;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export interface ProductImage {
  id: number;
  src: string;
  alt: string;
}

// Response types for API
export interface ProductsResponse {
  data: {
    data: IProductSingleItem[];
    totalCount: number;
    totalPages: number;
  };
}

export interface ProductResponse {
  data: IProductSingleItem;
}

// Form types for creating/updating products
export interface ProductFormData {
  title: string;
  description: string;
  price: number;
  category: string;
  imageUrl: string;
  images?: ProductImage[];
  isNew?: boolean;
  stock?: number;
  details?: string;
  ingredients?: string[];
  howToUse?: string;
  benefits?: string[];
  status?: 'active' | 'inactive';
}
