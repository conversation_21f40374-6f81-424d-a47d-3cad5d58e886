import { ICategoryItem } from "./category";
import { IDateValue } from "./common";

// First, define the input types at the top of your file
type BaseInput = {
  type: string;
  name: string;
  placeholder: string;
  required: boolean;
};

type UnitInput = BaseInput & {
  unit: string;
};

type Input = BaseInput | UnitInput;

// Update the questions array type
export type Question = {
  id: number;
  question: {
    title: string;
    description?: string;
  };
  type: "text" | "select" | "checkbox" | "radio" | "multiselect";
  inputs: Input[];
  image?: string;
  error?: {
    title: string;
    description: string;
  };
  // Two different ways to specify error conditions
  errorConditions?: [
    {
      select: string;
    },
  ];
  // Alternative way to specify error condition (used in implementation)
  showError?: {
    select: string;
  };
};

export type IQuestionnaireRule = {
  value: string;
  message: string;
  blockBooking: boolean;
  _id?: string;
};

export type IQuestionnaireOption = {
  label: string;
  value?: string;
  followUpQuestionId?: string | null;
  _id?: string;
};

export type IQuestionnaireSingleItem = {
  _id?: string;
  question?: string;
  description?: string;
  type?: "text" | "select" | "checkbox" | "radio" | "multiselect" | "number" | "date";
  unit?: string;
  name_ur?: string;
  cover_image?: any;
  createdAt?: IDateValue;
  categories?: ICategoryItem[];
  options?: IQuestionnaireOption[];
  rules?: IQuestionnaireRule[];
  isActive?: boolean;
  isOptional?: boolean;
  isFollowUp?: boolean;
  followUpTo?: string;
  deletedAt?: IDateValue | null;
  updatedAt?: IDateValue;
  bookingPhase?: string;
};

export interface IAnswerSingleItem {
  _id: string;
  question: IQuestionnaireSingleItem;
  user: string;
  answer: string;
  note?: string;
  followUpTo?: {
    _id: string;
    question: string;
    user: string;
    answer: string;
    note?: string;
    followUpTo?: string | null;
    createdAt: IDateValue;
    updatedAt: IDateValue;
  } 
  | null;
  createdAt?: IDateValue;
  updatedAt?: IDateValue;
}

export interface QuestionnariesPublicGetResponse {
  data: {
    data: IQuestionnaireSingleItem[];
    totalCount: number;
    totalPages: number;
  };
}

export interface PrebookingAnswersGetResponse {
  message: string;
  data: {
    data: IAnswerSingleItem[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
    hasAnsweredAll: boolean;
  };
}
