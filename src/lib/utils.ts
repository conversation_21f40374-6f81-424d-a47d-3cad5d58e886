import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getBookingStatusColor = (status: string) => {
  switch (status) {
    case "confirmed":
      return "success";
    case "pending":
      return "warning";
    case "completed":
      return "primary";
    case "cancelled":
      return "error";
    default:
      return "secondary";
  }
};

export function formatBookingDateTime(isoString: string) {
  const date = new Date(isoString);

  console.log("date", date);

  // Format date: Tue, 4 Mar, 2025
  const dateString = date
    .toLocaleDateString("en-US", {
      weekday: "short",
      day: "numeric",
      month: "short",
      year: "numeric",
    })
    .replace(",", "")
    .replace(/(\w+)\s(\d+)\s(\w+)\s(\d+)/, "$1, $2 $3, $4");

  // Format time: 01:30 pm
  const timeString = date
    .toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
      timeZone: "UTC",
    })
    .toLowerCase();

  return { dateString, timeString };
}