import { DM_Sans } from "next/font/google";
import localFont from "next/font/local";

// Google Fonts
// export const syneFont = Syne({
//   subsets: ["latin"],
//   weight: ["400", "500", "700"],
//   variable: "--font-syne",
// });

export const dmSansFont = DM_Sans({
  subsets: ["latin"],
  weight: ["300", "400", "500", "700"],
  variable: "--font-dm-sans",
});

// // Local Fonts
// export const univaNovaFont = localFont({
//   src: [
//     {
//       path: "../../public/fonts/univa-nova/UnivaNova-Light.ttf",
//       weight: "300",
//       style: "normal",
//     },
//     {
//       path: "../../public/fonts/univa-nova/UnivaNova-Regular.ttf",
//       weight: "400",
//       style: "normal",
//     },
//     {
//       path: "../../public/fonts/univa-nova/UnivaNova-Medium.ttf",
//       weight: "500",
//       style: "normal",
//     },
//     {
//       path: "../../public/fonts/univa-nova/UnivaNova-SemiBold.ttf",
//       weight: "600",
//       style: "normal",
//     },
//     {
//       path: "../../public/fonts/univa-nova/UnivaNova-Bold.ttf",
//       weight: "700",
//       style: "normal",
//     },
//     {
//       path: "../../public/fonts/univa-nova/UnivaNova-Heavy.ttf",
//       weight: "800",
//       style: "normal",
//     },
//   ],
//   variable: "--font-univa-nova",
// });

export const henjuFont = localFont({
  src: [
    {
      path: "../../public/fonts/henju/Henju-Thin.ttf",
      weight: "100",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-ThinSlant.ttf",
      weight: "100",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-ExtraLight.ttf",
      weight: "200",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-ExtraLightSlanted.ttf",
      weight: "200",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-Light.ttf",
      weight: "300",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-LightSlanted.ttf",
      weight: "300",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-Regular.ttf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-Slanted.ttf",
      weight: "400",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-Medium.ttf",
      weight: "500",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-MediumSlanted.ttf",
      weight: "500",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-SemiBold.ttf",
      weight: "600",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-SemiBoldSlanted.ttf",
      weight: "600",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-Bold.ttf",
      weight: "700",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-BoldSlanted.ttf",
      weight: "700",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-ExtraBold.ttf",
      weight: "800",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-ExtraBoldSlanted.ttf",
      weight: "800",
      style: "italic",
    },
    {
      path: "../../public/fonts/henju/Henju-Black.ttf",
      weight: "900",
      style: "normal",
    },
    {
      path: "../../public/fonts/henju/Henju-BlackSlant.ttf",
      weight: "900",
      style: "italic",
    },
  ],
  variable: "--font-henju",
});

export const quinnFont = localFont({
  src: [
    {
      path: "../../public/fonts/quinn/Quinn Rounded Light.otf",
      weight: "300",
      style: "normal",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Light Italic.otf",
      weight: "300",
      style: "italic",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Regular.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Italic.otf",
      weight: "400",
      style: "italic",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Semi Bold.otf",
      weight: "600",
      style: "normal",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Semi Bold Italic.otf",
      weight: "600",
      style: "italic",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Bold.otf",
      weight: "700",
      style: "normal",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Bold Italic.otf",
      weight: "700",
      style: "italic",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Extra Bold.otf",
      weight: "800",
      style: "normal",
    },
    {
      path: "../../public/fonts/quinn/Quinn Rounded Extra Bold Italic.otf",
      weight: "800",
      style: "italic",
    },
  ],
  variable: "--font-quinn",
});
