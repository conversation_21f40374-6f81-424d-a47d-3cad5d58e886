import { initializeApp, getApps, FirebaseApp } from "firebase/app";
import { getAuth, GoogleAuth<PERSON>rovider, OAuthProvider, signInWithPopup } from "firebase/auth";

const firebaseConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID!,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID!,
    measurementId: process.env.NEXT_PUBLIC_MEASUREMENT_ID!
};

// Initialize Firebase if not already initialized
let app: FirebaseApp;
if (!getApps().length) {
    app = initializeApp(firebaseConfig);
} else {
    app = getApps()[0];
}

export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
export const appleProvider = new OAuthProvider('apple.com');

// Configure Google sign-in
googleProvider.setCustomParameters({
    prompt: "select_account",
});

// Configure Apple sign-in
appleProvider.addScope('email');
appleProvider.addScope('name');


export async function signInWithGoogle(): Promise<string> {
    try {
        const result = await signInWithPopup(auth, googleProvider);
        const credential = GoogleAuthProvider.credentialFromResult(result);
        console.log("google credential", credential);
        const idToken = credential?.idToken;
        if (!idToken) {
            throw new Error("No access token available");
        }
        return idToken;
    } catch (error) {
        console.error("Google sign-in error:", error);
        throw error;
    }
}

export async function signInWithApple(): Promise<string> {
    try {
        console.log("apple sign in called");
        const result = await signInWithPopup(auth, appleProvider);
        const credential = OAuthProvider.credentialFromResult(result);
        const idToken = credential?.idToken;
        console.log("apple credential", credential);
        if (!idToken) {
            throw new Error("No access token available");
        }
        return idToken;
    } catch (error) {
        console.error("Apple sign-in error:", error);
        throw error;
    }
}