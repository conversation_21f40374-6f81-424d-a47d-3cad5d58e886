import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { authService } from '../api/auth-service';

// Create axios instance
const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'https://api.revolved.mvp-apps.ae',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Track if token refresh is in progress
let isRefreshing = false;
// Store pending requests that should be retried after token refresh
let failedQueue: {
  resolve: (value: unknown) => void;
  reject: (reason?: any) => void;
  config: AxiosRequestConfig;
}[] = [];

// Process the queue of failed requests
const processQueue = (error: AxiosError | null, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      // Retry the request with new token
      if (token && prom.config.headers) {
        prom.config.headers['Authorization'] = `Bearer ${token}`;
      }
      prom.resolve(axiosInstance(prom.config));
    }
  });

  // Reset the queue
  failedQueue = [];
};

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Get token from localStorage on client side
    if (typeof window !== 'undefined') {
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        try {
          const { state } = JSON.parse(authState);
          const token = state?.token;

          if (token && config.headers) {
            config.headers['Authorization'] = `Bearer ${token}`;
          }
        } catch (error) {
          console.error('Error parsing auth state:', error);
        }
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Handle 401 Unauthorized errors (token expired)
    if (
      error.response?.status === 401 &&
      originalRequest &&
      !originalRequest._retry &&
      typeof window !== 'undefined' // Only in browser
    ) {
      if (isRefreshing) {
        // If token refresh is already in progress, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject, config: originalRequest });
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Get refresh token from localStorage
        const authState = localStorage.getItem('auth-storage');
        if (!authState) {
          throw new Error('No authentication data found');
        }

        const { state } = JSON.parse(authState);
        const refreshToken = state?.refreshToken;

        if (!refreshToken) {
          throw new Error('No refresh token found');
        }

        // Call the auth service directly to refresh the token
        const response = await authService.refreshToken(refreshToken);

        // Update the token in localStorage
        try {
          const parsedState = JSON.parse(authState);
          parsedState.state.token = response?.data?.accessToken;
          parsedState.state.refreshToken = response?.data?.refreshToken;
          parsedState.state.user = response?.data?.user;
          parsedState.state.isAuthenticated = true;
          localStorage.setItem('auth-storage', JSON.stringify(parsedState));

          // Process the queue with the new token
          processQueue(null, response?.data?.accessToken);

          // Retry the original request with the new token
          if (originalRequest.headers) {
            originalRequest.headers['Authorization'] = `Bearer ${response?.data?.accessToken}`;
          }
          return axiosInstance(originalRequest);
        } catch (error) {
          console.error('Error updating auth state:', error);
          throw error;
        }
      } catch (refreshError) {
        // Token refresh failed, reject all queued requests
        processQueue(refreshError as AxiosError);

        // Clear auth state from localStorage on refresh failure
        try {
          const authState = localStorage.getItem('auth-storage');
          if (authState) {
            const parsedState = JSON.parse(authState);
            parsedState.state.token = null;
            parsedState.state.refreshToken = null;
            parsedState.state.user = null;
            parsedState.state.isAuthenticated = false;
            localStorage.setItem('auth-storage', JSON.stringify(parsedState));
          }
        } catch (error) {
          console.error('Error clearing auth state:', error);
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // Handle other errors
    return Promise.reject(error);
  }
);

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  try {
    const [url, config] = Array.isArray(args) ? args : [args, {}];

    const res = await axiosInstance.get(url, { ...config });

    return res.data;
  } catch (error) {
    // Silent error handling to avoid console pollution
    throw error;
  }
};

// ----------------------------------------------------------------------

export const revolvedCreator = async (args: string | [string, any]) => {
  const [url, data] = Array.isArray(args) ? args : [args, {}];

  const res = await axiosInstance.post(url, data);
  return res.data;
};

// ----------------------------------------------------------------------

export const revolvedCreatorPutMethod = async (args: string | [string, any]) => {
  const [url, data] = Array.isArray(args) ? args : [args, {}];

  const res = await axiosInstance.put(url, data);
  return res.data;
};

export const revolvedPatcher = async (args: string | [string, any]) => {
  const [url, data] = Array.isArray(args) ? args : [args, {}];  
  const res = await axiosInstance.patch(url, data);
  return res.data;
};

// ----------------------------------------------------------------------

export const revolvedSmasher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args, {}];

  const res = await axiosInstance.delete(url, { ...config });

  return res.data;
};

export default axiosInstance;
