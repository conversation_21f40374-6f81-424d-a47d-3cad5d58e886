"use client";

import {
  submitPrebookingAnswer,
  useGetPrebookingAnswers,
  useGetPublicQuestionnaires,
} from "@/api/questionnaire-service";
import { debounce } from "@/lib/debounce";
import { IQuestionnaireSingleItem } from "@/types/questionnaire";
import { useRouter } from "next/navigation";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

interface Answer {
  value: string;
  note: string;
}

interface QuestionnaireContextProps {
  mainQuestions: IQuestionnaireSingleItem[];
  followUpQuestions: Record<string, IQuestionnaireSingleItem>;
  currentQuestion: number;
  answers: Record<string, Answer>;
  validationError: string | null;
  isLoading: boolean;
  showWelcome: boolean;
  progress: number;
  currentCategoryId: string | null;
  isFinishedUserPrebooking: boolean;
  isEditMode: boolean;
  handleStartQuestionnaire: () => void;
  handleInputChange: (questionId: string, name: string, value: string) => void;
  handleCheckboxChange: (questionId: string, name: string) => void;
  handleContinue: () => void;
  handleBack: () => void;
  hasError: () => boolean;
  validateCurrentQuestion: () => boolean;
  handleChangeCategoryId: (categoryId: string) => void;
  updateAnswers: (answers: Record<string, any>) => void;
  updateIsFinishedUserPrebooking: (isFinished: boolean) => void;
  setIsEditMode: (isEdit: boolean) => void;
}

const QuestionnaireContext = createContext<QuestionnaireContextProps | undefined>(undefined);

export const QuestionnaireProvider = ({
  children,
  categoryId,
}: {
  children: React.ReactNode;
  categoryId?: string;
}) => {
  const [currentCategoryId, setCurrentCategoryId] = useState<string | null>(categoryId || null);
  const [mainQuestions, setMainQuestions] = useState<IQuestionnaireSingleItem[]>([]);
  const [followUpQuestions, setFollowUpQuestions] = useState<
    Record<string, IQuestionnaireSingleItem>
  >({});
  const [showWelcome, setShowWelcome] = useState(true);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, Answer>>({});
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isFinishedUserPrebooking, setIsFinishedUserPrebooking] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  const { questionnaires, questionnairesLoading } = useGetPublicQuestionnaires({
    category: currentCategoryId,
    bookingPhase: "prebooking",
  });

  const {
    prebookingAnswers,
    prebookingAnswersLoading,
    revalidatePrebookingAnswers,
    hasAnsweredAll,
  } = useGetPrebookingAnswers({
    limit: 100,
    page: 0,
  });

  const router = useRouter();

  // Memoize progress calculation
  const progress = useMemo(() => {
    if (!mainQuestions.length) return 0;
    return ((currentQuestion + 1) / mainQuestions.length) * 100;
  }, [currentQuestion, mainQuestions.length]);

  // Only set finished state if all prebooking answers are loaded and all questions are answered
  useEffect(() => {
    if (
      !prebookingAnswersLoading &&
      !questionnairesLoading &&
      prebookingAnswers &&
      hasAnsweredAll &&
      !isEditMode
    ) {
      setIsFinishedUserPrebooking(true);
    }
  }, [prebookingAnswersLoading, questionnairesLoading, prebookingAnswers, hasAnsweredAll]);

  // Process questionnaires data when it loads
  useEffect(() => {
    if (!questionnairesLoading && questionnaires && questionnaires.length > 0) {
      const mainQs = questionnaires.filter((item) => !item.isFollowUp);
      setMainQuestions(mainQs);
      const followUpMap: Record<string, IQuestionnaireSingleItem> = {};
      questionnaires.forEach((item) => {
        if (item.isFollowUp && item._id) {
          followUpMap[item._id] = item;
        }
      });
      setFollowUpQuestions(followUpMap);
    }
  }, [questionnairesLoading, questionnaires]);

  // Utility to validate a single answer for a question
  const validateAnswer = useCallback((question: IQuestionnaireSingleItem, answer: any): boolean => {
    if (!question) return false;
    if (question.rules && question.rules.length > 0) {
      if (
        question.rules.some((condition) => {
          if (condition.value && answer?.value?.toLowerCase() === condition.value.toLowerCase()) {
            return true;
          }
          return false;
        })
      ) {
        return false;
      }
    }
    if (question.type === "select" && !answer?.value) return false;
    if (question.type === "multiselect" && !answer?.value) return false;
    return true;
  }, []);

  // Stable debounced submit function
  const debouncedSubmitAnswer = useCallback(
    debounce(
      async (
        question: IQuestionnaireSingleItem,
        answer: any,
        parentQuestionId?: string,
        isFollowUp?: boolean
      ) => {
        if (!validateAnswer(question, answer)) return;
        let valueToSubmit = answer?.value;
        if (valueToSubmit && typeof valueToSubmit === "object" && !Array.isArray(valueToSubmit)) {
          valueToSubmit = Object.keys(valueToSubmit).filter((k) => valueToSubmit[k]);
        }
        if (Array.isArray(valueToSubmit)) {
          valueToSubmit = valueToSubmit.join(",");
        }
        console.log("prebooking answers", prebookingAnswers);
        const parentAnswer = prebookingAnswers.find((a) => a.question._id === parentQuestionId);
        console.log("parent question answer", parentAnswer);
        console.log("submitting answer", {
          parentQuestionId,
          parentAnswer: parentAnswer?._id,
          questionId: question._id,
          answer: valueToSubmit || "",
          isFollowUp,
        });
        const answerPayload = {
          questionId: question._id,
          answer: valueToSubmit || "",
          followUpTo: isFollowUp ? parentAnswer?._id : undefined,
          note: answer?.note || "",
        };
        try {
          await Promise.all([submitPrebookingAnswer(answerPayload), revalidatePrebookingAnswers()]);
        } catch (error) {
          // Optionally handle error
        }
      },
      800
    ),
    [prebookingAnswers, validateAnswer, revalidatePrebookingAnswers]
  );

  // Memoize mainQuestions and followUpQuestions for stable references
  const mainQuestionsRef = useRef(mainQuestions);
  const followUpQuestionsRef = useRef(followUpQuestions);
  useEffect(() => {
    mainQuestionsRef.current = mainQuestions;
  }, [mainQuestions]);
  useEffect(() => {
    followUpQuestionsRef.current = followUpQuestions;
  }, [followUpQuestions]);

  // Optimized input change handler
  const handleInputChange = useCallback(
    (questionId: string, name: string, value: string) => {
      setAnswers((prev) => {
        const updated = {
          ...prev,
          [questionId]: {
            value: name === "value" ? value : value || "",
            note: name === "note" ? value : prev[questionId]?.note || "",
          },
        };
        return updated;
      });
      setValidationError(null);

      const currentQ = mainQuestionsRef.current.find((q) => q._id === questionId);
      const answerObj = {
        value: name === "value" ? value : value || "",
        note: name === "note" ? value : answers[questionId]?.note || "",
      };
      if (currentQ && validateAnswer(currentQ, answerObj)) {
        // console.log("answer obj", answerObj);
        debouncedSubmitAnswer(currentQ, answerObj);
      }
      Object.values(followUpQuestionsRef.current).forEach((fq) => {
        if (fq._id === questionId && answers[fq._id]) {
          console.log("main questions", mainQuestionsRef.current);
          console.log("follow up questions", questionId);
          const parentQuestion = mainQuestionsRef.current.find((q) => {
            return q.options.some((o) => o.followUpQuestionId === questionId);
          });
          const answerObj = {
            ...answers[fq._id],
            value: value,
          };
          if (validateAnswer(fq, answers[fq._id])) {
            console.log("answer obj", answerObj, parentQuestion);
            debouncedSubmitAnswer(fq, answerObj, parentQuestion?._id, true);
          }
        }
      });
    },
    [answers, validateAnswer, debouncedSubmitAnswer]
  );

  // Optimized checkbox handler
  const handleCheckboxChange = useCallback(
    (questionId: string, name: string) => {
      setAnswers((prev) => {
        const updated = {
          ...prev,
          [questionId]: {
            value: name,
            note: prev[questionId]?.note || "",
          },
        };
        return updated;
      });
      setValidationError(null);
      const currentQ = mainQuestionsRef.current.find((q) => q._id === questionId);
      const answerObj = {
        value: name,
        note: answers[questionId]?.note || "",
      };
      if (currentQ && validateAnswer(currentQ, answerObj)) {
        debouncedSubmitAnswer(currentQ, answerObj);
      }
      Object.values(followUpQuestionsRef.current).forEach((fq) => {
        if (fq._id === questionId && answers[fq._id]) {
          if (validateAnswer(fq, answers[fq._id])) {
            debouncedSubmitAnswer(fq, answers[fq._id]);
          }
        }
      });
    },
    [answers, validateAnswer, debouncedSubmitAnswer]
  );

  // Error check for current question
  const hasError = useCallback(() => {
    const currentQ = mainQuestions[currentQuestion];
    if (!currentQ) return false;
    if (currentQ.rules && currentQ.rules.length > 0) {
      return currentQ.rules.some((condition) => {
        if (
          condition.value &&
          answers[currentQ._id]?.value?.toLowerCase() === condition.value.toLowerCase()
        ) {
          return true;
        }
        return false;
      });
    }
    return false;
  }, [answers, currentQuestion, mainQuestions]);

  // Validate current question
  const validateCurrentQuestion = useCallback((): boolean => {
    const question = mainQuestions[currentQuestion];
    if (!question) return true;
    setValidationError(null);
    if (hasError()) return false;
    if (question.type === "select") {
      if (!answers[question._id]?.value) {
        setValidationError("Please select an option to continue");
        return false;
      }
    } else if (question.type === "multiselect") {
      if (!answers[question._id]?.value) {
        setValidationError("Please select at least one option to continue");
        return false;
      }
    }
    return true;
  }, [answers, currentQuestion, mainQuestions, hasError]);

  // Continue handler
  const handleContinue = useCallback(() => {
    if (!validateCurrentQuestion()) {
      return;
    }
    if (currentQuestion === mainQuestions.length - 1) {
      // router.push(`/booking?categoryId=${currentCategoryId}`);
      setIsFinishedUserPrebooking(true);
      setCurrentQuestion(0);
    } else {
      setCurrentQuestion((prev) => prev + 1);
      setValidationError(null);
    }
  }, [currentQuestion, mainQuestions.length, validateCurrentQuestion, router, currentCategoryId]);

  // Back handler
  const handleBack = useCallback(() => {
    setCurrentQuestion((prev) => (prev > 0 ? prev - 1 : 0));
  }, []);

  // Category change handler
  const handleChangeCategoryId = useCallback((categoryId: string) => {
    setCurrentCategoryId(categoryId);
  }, []);

  // Answers update
  const updateAnswers = useCallback((newAnswers: Record<string, any>) => {
    setAnswers(newAnswers);
  }, []);

  // Prebooking finished update
  const updateIsFinishedUserPrebooking = useCallback((isFinished: boolean) => {
    setIsFinishedUserPrebooking(isFinished);
  }, []);

  // Start questionnaire handler
  const handleStartQuestionnaire = useCallback(() => {
    setShowWelcome(false);
  }, []);

  // Memoize context value for performance
  const value = useMemo(
    () => ({
      mainQuestions,
      followUpQuestions,
      currentQuestion,
      answers,
      validationError,
      isLoading: questionnairesLoading,
      showWelcome,
      progress,
      currentCategoryId,
      handleStartQuestionnaire,
      handleInputChange,
      handleCheckboxChange,
      handleContinue,
      handleBack,
      hasError,
      validateCurrentQuestion,
      handleChangeCategoryId,
      isFinishedUserPrebooking,
      updateAnswers,
      updateIsFinishedUserPrebooking,
      isEditMode,
      setIsEditMode,
    }),
    [
      mainQuestions,
      followUpQuestions,
      currentQuestion,
      answers,
      validationError,
      questionnairesLoading,
      showWelcome,
      progress,
      currentCategoryId,
      handleStartQuestionnaire,
      handleInputChange,
      handleCheckboxChange,
      handleContinue,
      handleBack,
      hasError,
      validateCurrentQuestion,
      handleChangeCategoryId,
      isFinishedUserPrebooking,
      updateAnswers,
      updateIsFinishedUserPrebooking,
      isEditMode,
      setIsEditMode,
    ]
  );

  return <QuestionnaireContext.Provider value={value}>{children}</QuestionnaireContext.Provider>;
};

export const useQuestionnaire = () => {
  const context = useContext(QuestionnaireContext);
  if (context === undefined) {
    throw new Error("useQuestionnaire must be used within a QuestionnaireProvider");
  }
  return context;
};
